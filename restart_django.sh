#!/bin/bash

if [ -f .env ]; then
    set -a
    source .env
    set +a
fi

# Gunicorn 프로세스 종료
GUNICORN_PIDS=$(pgrep -f "gunicorn.*:44561")
if [ -n "$GUNICORN_PIDS" ]; then
    echo "Stopping Gunicorn processes:"
    for PID in $GUNICORN_PIDS; do
        echo "Stopping Gunicorn process: $PID"
        kill -9 $PID
    done
fi

# mail_fetch_thread.py 프로세스 종료
MAIL_FETCH_PIDS=$(pgrep -f "mail_fetch_thread.py")
if [ -n "$MAIL_FETCH_PIDS" ]; then
    echo "Stopping mail_fetch_thread.py processes:"
    for PID in $MAIL_FETCH_PIDS; do
        echo "Stopping mail_fetch_thread.py process: $PID"
        kill -9 $PID
    done
fi

# 기존 log_db_manage.py 프로세스 종료 (이는 이제 cron으로 처리되며 여기서는 직접 실행하지 않음)
LOG_DB_MANAGE_PIDS=$(pgrep -f "log_db_manage.py")
if [ -n "$LOG_DB_MANAGE_PIDS" ]; then
    echo "Stopping log_db_manage.py processes:"
    for PID in $LOG_DB_MANAGE_PIDS; do
        echo "Stopping log_db_manage.py process: $PID"
        kill -9 $PID
    done
fi

# 운영 환경일 때만 collectstatic
if [ "$ENVIRONMENT" == "prod" ] || [ "$ENVIRONMENT" == "dev" ]; then
    echo "Running collectstatic..."
    echo "yes" | python manage.py collectstatic > /dev/null 2>&1
fi

# mail_fetch_thread.py를 백그라운드에서 실행하고 PID를 저장
echo "Starting mail_fetch_thread.py with hourly restart..."
nohup bash -c "while true; do
    timeout 3600 python mail_fetch_thread.py
    sleep 1
done" >/dev/null 2>&1 &
MAIL_FETCH_PID=$!
echo "mail_fetch_thread.py PID: $MAIL_FETCH_PID"

# 로그 DB 관리는 이제 crontab에서 관리하므로 여기서 실행하지 않음
# echo "Running log_db_manage.py..."
# nohup python log_db_manage.py >/dev/null 2>&1 &

# Gunicorn 시작
echo "Starting Gunicorn... port: 44561"
nohup gunicorn --bind 0.0.0.0:44561 mysite.wsgi:application >/dev/null 2>&1 &  # 백그라운드 실행 및 출력 닫기
