{% load static %}
<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>키오스크 관리 대시보드</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .stat-card.total {
            border-left-color: #3498db;
        }

        .stat-card.latest {
            border-left-color: #2ecc71;
        }

        .stat-card.outdated {
            border-left-color: #e74c3c;
        }

        .stat-card.active {
            border-left-color: #f39c12;
        }

        .stat-card.inactive {
            border-left-color: #95a5a6;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1rem;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .version-info {
            background: #e8f5e8;
            border: 2px solid #2ecc71;
            border-radius: 10px;
            padding: 20px;
            margin: 0 30px 30px 30px;
            text-align: center;
        }

        .version-info h3 {
            color: #27ae60;
            margin-bottom: 10px;
            font-size: 1.3rem;
        }        .version-info .version {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
            background: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-right: 15px;
        }

        .update-version-btn {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
            margin-left: 10px;
        }

        .update-version-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
        }

        .modal h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .modal-buttons {
            display: flex;
            justify-content: space-between;
            gap: 15px;
            margin-top: 25px;
        }

        .modal-btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .modal-btn.confirm {
            background: #27ae60;
            color: white;
        }

        .modal-btn.confirm:hover {
            background: #229954;
        }

        .modal-btn.cancel {
            background: #95a5a6;
            color: white;
        }

        .modal-btn.cancel:hover {
            background: #7f8c8d;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }        .close:hover {
            color: #e74c3c;
        }

        .edit-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .edit-btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
        }

        .edit-btn:active {
            transform: translateY(0);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .search-container {
            padding: 0 30px 20px 30px;
        }

        .search-box {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.2);
        }

        .kiosk-table-container {
            padding: 0 30px 30px 30px;
            overflow-x: auto;
        }

        .kiosk-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .kiosk-table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 20px 15px;
            text-align: left;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.9rem;
        }

        .kiosk-table td {
            padding: 20px 15px;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }

        .kiosk-table tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        .version-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            text-align: center;
            display: inline-block;
            min-width: 80px;
        }

        .version-latest {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }

        .version-outdated {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }

        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            text-align: center;
            display: inline-block;
            min-width: 60px;
        }

        .status-active {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #bee5eb;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }

        .org-type {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: bold;
            display: inline-block;
        }

        .org-school {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .org-organization {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .org-na {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .logout-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #e74c3c;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            text-decoration: none;
            transition: background-color 0.3s ease, transform 0.3s ease;
            z-index: 1000;
        }

        .logout-btn:hover {
            background: #c0392b;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #3498db;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 50%;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 1000;
        }

        .refresh-btn:hover {
            background: #2980b9;
            transform: rotate(180deg) scale(1.1);
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 1.2rem;
        }

        .loading {
            text-align: center;
            padding: 40px;
            font-size: 1.2rem;
            color: #3498db;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .kiosk-table-container {
                padding: 0 15px 20px 15px;
            }

            .kiosk-table th,
            .kiosk-table td {
                padding: 15px 10px;
                font-size: 0.9rem;
            }

            .logout-btn {
                top: 10px;
                right: 10px;
                padding: 10px 20px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <a href="{% url 'logout' %}" class="logout-btn">로그아웃</a>
    
    <div class="dashboard-container">
        <div class="header">
            <h1>🖥️ 키오스크 관리 대시보드</h1>
            <p>전체 키오스크 현황 및 버전 관리</p>
        </div>        <div class="version-info">
            <h3>📋 현재 최신 버전</h3>
            <div class="version" id="currentVersion">{{ latest_version }}</div>
            <button class="update-version-btn" onclick="openUpdateModal()">🔄 버전 업데이트</button>
        </div>

        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-number">{{ total_kiosks }}</div>
                <div class="stat-label">전체 키오스크</div>
            </div>
            <div class="stat-card latest">
                <div class="stat-number">{{ latest_count }}</div>
                <div class="stat-label">최신 버전</div>
            </div>
            <div class="stat-card outdated">
                <div class="stat-number">{{ outdated_count }}</div>
                <div class="stat-label">업데이트 필요</div>
            </div>
            <div class="stat-card active">
                <div class="stat-number">{{ active_count }}</div>
                <div class="stat-label">활성 상태</div>
            </div>
            <div class="stat-card inactive">
                <div class="stat-number">{{ inactive_count }}</div>
                <div class="stat-label">비활성 상태</div>
            </div>
        </div>

        <div class="search-container">
            <input type="text" id="searchInput" class="search-box" placeholder="🔍 키오스크 ID, 위치, 기관명, 버전, 비고로 검색...">
        </div>

        <div class="kiosk-table-container">
            <table class="kiosk-table" id="kioskTable">                <thead>
                    <tr>
                        <th>키오스크 ID</th>
                        <th>버전</th>
                        <th>위치</th>
                        <th>소속 기관</th>
                        <th>기관 유형</th>
                        <th>상태</th>
                        <th>등록일</th>
                        <th>마지막 사용</th>
                        <th>비고</th>
                        <th>관리</th>
                    </tr>
                </thead>
                <tbody id="kioskTableBody">
                    {% for kiosk in kiosks %}
                    <tr class="kiosk-row" data-search="{{ kiosk.kiosk_id|lower }} {{ kiosk.location|lower }} {{ kiosk.organization|lower }} {{ kiosk.version|lower }} {{ kiosk.remark|lower }}">
                        <td><strong>{{ kiosk.kiosk_id }}</strong></td>
                        <td>
                            <span class="version-badge {% if kiosk.is_latest %}version-latest{% else %}version-outdated{% endif %}">
                                {{ kiosk.version }}
                            </span>
                        </td>
                        <td>{{ kiosk.location }}</td>
                        <td>{{ kiosk.organization }}</td>
                        <td>
                            <span class="org-type {% if kiosk.organization_type == '학교' %}org-school{% elif kiosk.organization_type == '기관' %}org-organization{% else %}org-na{% endif %}">
                                {{ kiosk.organization_type }}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge {% if kiosk.active %}status-active{% else %}status-inactive{% endif %}">
                                {% if kiosk.active %}활성{% else %}비활성{% endif %}
                            </span>                        </td>                        <td>{{ kiosk.created_dt|date:"Y-m-d H:i" }}</td>
                        <td>
                            {% if kiosk.last_used_dt %}
                                {{ kiosk.last_used_dt|date:"Y-m-d H:i" }}
                            {% else %}
                                <span style="color: #999;">사용 기록 없음</span>
                            {% endif %}
                        </td>
                        <td>{{ kiosk.remark }}</td>
                        <td>
                            <a href="/admin/analysis/kioskinfo/{{ kiosk.id }}/change/" class="edit-btn" target="_blank">편집</a>
                        </td>
                    </tr>                    {% empty %}
                    <tr>
                        <td colspan="10" class="no-results">등록된 키오스크가 없습니다.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>    </div>

    <!-- 버전 업데이트 모달 -->
    <div id="updateModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeUpdateModal()">&times;</span>
            <h2>🔄 최신 버전 업데이트</h2>
            <form id="updateVersionForm">
                {% csrf_token %}
                <div class="form-group">
                    <label for="newVersion">새 버전:</label>
                    <input type="text" id="newVersion" name="new_version" placeholder="예: v2.1.0" required>
                </div>
                <div class="modal-buttons">
                    <button type="button" class="modal-btn cancel" onclick="closeUpdateModal()">취소</button>
                    <button type="submit" class="modal-btn confirm">업데이트</button>
                </div>
            </form>
        </div>
    </div>

    <button class="refresh-btn" onclick="location.reload();">↻</button>

    <script>
        // 검색 기능
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('.kiosk-row');
            let visibleCount = 0;

            rows.forEach(row => {
                const searchData = row.getAttribute('data-search');
                if (searchData.includes(searchTerm)) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            // 검색 결과가 없을 때 메시지 표시
            const tbody = document.getElementById('kioskTableBody');
            const noResultsRow = tbody.querySelector('.no-results-row');              if (visibleCount === 0 && searchTerm !== '') {
                if (!noResultsRow) {
                    const row = document.createElement('tr');
                    row.className = 'no-results-row';
                    row.innerHTML = '<td colspan="10" class="no-results">검색 결과가 없습니다.</td>';
                    tbody.appendChild(row);
                }
            } else if (noResultsRow) {
                noResultsRow.remove();
            }
        });

        // 페이지 로드 애니메이션
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            const rows = document.querySelectorAll('.kiosk-row');
            rows.forEach((row, index) => {
                row.style.opacity = '0';
                row.style.transform = 'translateX(-30px)';
                setTimeout(() => {
                    row.style.transition = 'all 0.6s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateX(0)';
                }, 500 + (index * 50));
            });
        });

        // 자동 새로고침 (5분마다)
        setInterval(function() {
            location.reload();
        }, 300000); // 5분 = 300,000ms

        // 키보드 단축키
        document.addEventListener('keydown', function(e) {
            // Ctrl + R 또는 F5로 새로고침
            if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
                e.preventDefault();
                location.reload();
            }
            
            // Ctrl + F로 검색 박스 포커스
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                document.getElementById('searchInput').focus();
            }
        });        // 툴팁 효과
        document.querySelectorAll('.version-badge, .status-badge').forEach(badge => {
            badge.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1)';
                this.style.transition = 'transform 0.2s ease';
            });
            
            badge.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // 버전 업데이트 모달 기능
        function openUpdateModal() {
            document.getElementById('updateModal').style.display = 'block';
            document.getElementById('newVersion').focus();
        }

        function closeUpdateModal() {
            document.getElementById('updateModal').style.display = 'none';
            document.getElementById('newVersion').value = '';
        }

        // 모달 바깥쪽 클릭 시 닫기
        window.onclick = function(event) {
            const modal = document.getElementById('updateModal');
            if (event.target === modal) {
                closeUpdateModal();
            }
        }

        // ESC 키로 모달 닫기
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeUpdateModal();
            }
        });

        // 버전 업데이트 폼 제출
        document.getElementById('updateVersionForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const newVersion = document.getElementById('newVersion').value.trim();
            if (!newVersion) {
                alert('새 버전을 입력해주세요.');
                return;
            }

            // 확인 대화상자
            if (!confirm(`정말로 최신 버전을 "${newVersion}"로 업데이트하시겠습니까?\n\n이 작업은 모든 키오스크의 버전 상태에 영향을 줍니다.`)) {
                return;
            }

            // AJAX 요청으로 버전 업데이트
            const formData = new FormData();
            formData.append('new_version', newVersion);
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

            fetch('{% url "update_latest_version" %}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 성공 시 화면 업데이트
                    document.getElementById('currentVersion').textContent = newVersion;
                    closeUpdateModal();
                    
                    // 성공 메시지 표시
                    alert(`최신 버전이 "${newVersion}"로 업데이트되었습니다.\n페이지를 새로고침하여 모든 키오스크의 상태를 확인하세요.`);
                    
                    // 잠시 후 페이지 새로고침
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    alert('버전 업데이트에 실패했습니다: ' + (data.message || '알 수 없는 오류'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('서버 연결 오류가 발생했습니다.');
            });
        });
    </script>
</body>
</html>
