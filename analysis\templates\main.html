{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}Main Page{% endblock %}

{% block content %}
<!-- Chart.js 라이브러리 추가 -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<!-- Chart.js 애니메이션 플러그인 -->
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>

<script>
    const message = "{{ message }}";

    if (message) {
        alert(message);
        window.location.href = "{% url 'main' %}";
    }

    const error_message = "{{ error_message }}";
    if (error_message) {
        alert(error_message);
        window.location.href = "{% url 'main' %}";
    }
    
    // 여러 개의 problem 메시지를 처리하는 함수
    function showMultipleAlerts(messages, delay = 0) {
        if (messages.length === 0) return;
        
        // 첫 번째 메시지는 바로 표시
        showGlobalAlert(messages[0]);
        
        // 남은 메시지들은 순차적으로 표시 (이전 알림이 완전히 보이고 나서)
        if (messages.length > 1) {
            setTimeout(function() {
                // 남은 메시지들로 재귀 호출 (첫 번째 요소 제외)
                showMultipleAlerts(messages.slice(1), 1000);
            }, 300 + delay); // 각 알림 사이에 시간 간격 추가
        }
    }
    
    {% if problem %}
        // 모든 problem 메시지를 배열로 수집
        const problemMessages = [
            {% for message in problem %}
                "{{ message }}",
            {% endfor %}
        ];
        
        // 수집된 메시지들을 순차적으로 표시
        showMultipleAlerts(problemMessages);
    {% endif %}
</script>

<div class="page-transition-wrapper">
    <div class="main-container">
        {% if has_affiliation %}
            <div class="welcome-section">
                {% if user_type == 'S' %} <!-- 소속이 학교 일 경우 학교 아이콘 -->
                <i class="fas fa-school" style="font-size: 3rem; color: #111;"></i>
                <h2 class="welcome-title">{{ user_affil }}</h2>
                {% else %} <!-- 소속이 기업일 경우 기업(기관) 아이콘 -->
                <i class="fas fa-building" style="font-size: 3rem; color: #111;"></i>
                <h2 class="welcome-title">{{ user_affil }} </h2>
                {% endif %}
                
                {% if user_type == 'S' or user_type == 'O' %}
                <a href="{% url 'member_register' %}" class="transition-link">
                    <button class="btn-register">
                        <i class="fas fa-user-plus"></i> 구성원 등록
                    </button>
                </a>
                {% endif %}

                <a href="{% url 'org_register' %}" class="transition-link">
                    <button class="btn-reregister">
                        <i class="fas fa-edit"></i> 기관 재등록
                    </button>
                </a>
                <div class="search-container">
                    <input type="text" id="userSearch" placeholder="사용자 검색..." />
                    <button id="searchButton">검색</button>
                </div>
            </div>
            <hr style="color: #333;"/>
            <div class="stats-overview">
                <h2 class="dashboard-title">검사 진행 현황 대시보드</h2>
                
                <!-- 진행률 요약 -->
                <div class="dashboard-container">
                    <!-- 왼쪽 진행률 도넛 차트 -->
                    <div class="dashboard-chart">
                        <div class="progress-chart-container">
                            <h3>검사 완료율</h3>
                            <div class="chart-wrapper">
                                <canvas id="progressChart" style="width: 100%"></canvas>
                            </div>
                            <div class="progress-legend">
                                <div class="legend-item">
                                    <span class="legend-color" style="background-color: rgba(75, 192, 192, 0.7);"></span>
                                    <span class="legend-text">완료 인원: {{ total_members|subtract:pending_tests }}명</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-color" style="background-color: rgba(255, 99, 132, 0.7);"></span>
                                    <span class="legend-text">미완료 인원: {{ pending_tests }}명</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 오른쪽 통계 그리드 -->
                    <div class="stats-grid">
                        <div class="stat-summary-card" data-type="members">
                            <div class="stat-icon-large">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content-large">
                                <h3>전체 구성원</h3>
                                <p class="stat-number-large">{{ total_members }}<span>명</span></p>
                                <div class="stat-desc">해당 기관에 등록된 총 인원수입니다</div>
                            </div>
                        </div>
                        
                        <div class="stat-summary-card" data-type="body">
                            <div class="stat-icon-large">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="stat-content-large">
                                <h3>체형 검사</h3>
                                <p class="stat-number-large">{{ total_results }}<span>건</span></p>
                                <div class="stat-desc">지금까지 완료된 체형 검사 총 건수입니다</div>
                            </div>
                        </div>
                        
                        {% if user_type == 'O' %}
                        <div class="stat-summary-card" data-type="gait">
                            <div class="stat-icon-large">
                                <i class="fas fa-walking"></i>
                            </div>
                            <div class="stat-content-large">
                                <h3>보행 검사</h3>
                                <p class="stat-number-large">{{ total_gait_results }}<span>건</span></p>
                                <div class="stat-desc">지금까지 완료된 보행 검사 총 건수입니다</div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <div class="stat-summary-card" data-type="month">
                            <div class="stat-icon-large">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="stat-content-large">
                                <h3>이번 달 검사</h3>
                                <p class="stat-number-large">{{ current_month_results }}<span>건</span></p>
                                <div class="stat-desc">이번 달에 실시된 검사의 총 건수입니다</div>
                            </div>
                        </div>
                        
                        <div class="stat-summary-card" data-type="pending">
                            <div class="stat-icon-large">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="stat-content-large">
                                <h3>미완료 인원</h3>
                                <p class="stat-number-large">{{ pending_tests }}<span>명</span></p>
                                <div class="stat-desc">검사를 완료하지 않은 총 인원수입니다</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="groups-section">
                <h3 class="section-title">
                    {% if user_type == 'S' %}
                        <i class="fas fa-graduation-cap"></i> 학년/반 현황
                    {% else %}
                        <i class="fas fa-sitemap"></i> 부서 현황
                    {% endif %}
                </h3>

                <div class="groups-container">
                    {% if user_type == 'S' %}
                        {% if group_structure|length == 0 %}
                            <div>
                            <h3>현재 년도에 등록된 학년/반이 없습니다.</h3>
                            <a href="{% url 'report' %}"><button class="view-button" > <i class="fas fa-search"></i>
                                <span>자세히 보기</span></button></a>
                            </div>
                            {% else %}
                        {% for group in group_structure  %}
                            <div class="grade-card">
                                <h4><i class="fa-solid fa-users-line"></i> {{ group.department }}</h4>
                                <div class="class-list">
                                    <form action="{% url 'report' %}" method="post" style="display: inline;">
                                        {% csrf_token %}
                                        <div class="class-info-card">
                                            <div class="class-info">
                                                {% comment %} <span class="class-number">{{ department }}반</span> {% endcomment %}
                                                <span class="student-count">{{ group.member_count }}명</span>
                                            </div>
                                            <input type="hidden" name="group" value="{{ group.department }}">
                                            <button type="submit" class="view-button">
                                                <i class="fas fa-search"></i>
                                                <span>조회</span>
                                            </button>
                                        </div>
                                    </form>
                                </div>

                            </div>
                        {% endfor %}
                            {% endif %}
                        {% elif user_type == 'O' %}
                            {% if group_structure|length == 0 %}                       
                                <h3>등록된 부서가 없습니다.</h3>
                            {% else %}
                                {% for dept, value in group_structure.items %}
                                <div class="grade-card dept-card">
                                    <div class="dept-info-card">
                                        <div class="dept-header">
                                            <i class="fas fa-building"></i>
                                            <div class="dept-details">
                                                <span class="dept-name">{{ dept }}</span>
                                                <span class="employee-count">{{ value }}명</span>
                                            </div>
                                        </div>
                                        <form action="{% url 'report' %}" method="post">
                                            {% csrf_token %}
                                            <input type="hidden" name="group" value="{{ dept }}">
                                            <button type="submit" class="view-dept-button">
                                                <i class="fas fa-search"></i>
                                                <span>조회</span>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                                {% endfor %}
                            {% endif %}
                    {% endif %}
                </div>
            </div>
        {% else %}
            <div class="welcome-section">
                <h2 class="welcome-title">기관 등록이 필요합니다</h2>
                <a href="{% url 'org_register' %}" class="transition-link">
                    <button class="btn-register">
                        <i class="fas fa-plus"></i> 기관 등록
                    </button>
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- 모달 창 -->
<div id="searchModal" class="modal">
    <div class="modal-content">
        <span class="close-button">&times;</span>
        <h2>검색 결과</h2>
        <h4 id="searchResultsCount">총 건의 검색 결과가 있습니다.</h4>
        <hr/>
        <div id="searchResults"></div>
    </div>
</div>

<script>
document.getElementById('searchButton').addEventListener('click', function() {
    const query = document.getElementById('userSearch').value;
    if (query) {
        fetch(`/search_user/?query=${query}`)
            .then(response => response.json())
            .then(data => {
                displaySearchResults(data.results, data.user_type);
            })
            .catch(error => console.error('Error:', error));
    }
});

function displaySearchResults(results, user_type) {
    const resultsContainer = document.getElementById('searchResults');
    const resultsCountElement = document.getElementById('searchResultsCount');
    resultsContainer.innerHTML = ''; // 이전 결과 초기화
    resultsCountElement.innerHTML = '';

    if (results.length === 0) {
        resultsContainer.innerHTML = '<p>검색 결과가 없습니다.</p>';
    } else {
        results_total_count = results.length
        resultsCountElement.innerHTML = `총 ${results_total_count}건의 검색 결과가 있습니다.`;
        results.forEach(user => {
            const userDiv = document.createElement('div');
            userDiv.className = 'user-card';
            userDiv.innerHTML = `
                <div class="s-user-info">
                    <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                            <p class="user-name">${user.user_display_name}</p>
                            <p class="user-class">${user.department}</p>
                        </div>
                    </div>
                `;
                resultsContainer.appendChild(userDiv);
            });
        }
        // 모달 창 표시
document.getElementById('searchModal').style.display = 'block';

    }


// 모달 창 닫기
document.querySelector('.close-button').addEventListener('click', function() {
    document.getElementById('searchModal').style.display = 'none';
});

// 차트 생성 코드 추가
document.addEventListener('DOMContentLoaded', function() {
    // 데이터 준비
    const totalResults = {{ total_results }};
    const currentMonthResults = {{ current_month_results }};
    const pendingTests = {{ pending_tests }};
    const totalMembers = {{ total_members }};
    {% if user_type == 'O' %}
    const totalGaitResults = {{ total_gait_results }};
    {% endif %}
    
    // 완료/미완료 비율 계산
    const completedTests = totalMembers - pendingTests;
    const completionRate = Math.round((completedTests / totalMembers) * 100) || 0;
    
    // 진행률 도넛 차트 생성
    if (document.getElementById('progressChart')) {
        // 차트 컨텍스트 가져오기
        const progressCtx = document.getElementById('progressChart').getContext('2d');
        
        // 도넛 차트 데이터
        const donutChartData = {
            labels: ['완료', '미완료'],
            datasets: [{
                data: [completedTests, pendingTests],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(255, 99, 132, 0.7)'
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 99, 132, 1)'
                ],
                borderWidth: 1
            }]
        };
        
        // 도넛 차트 옵션
        const donutChartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '70%',
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        font: {
                            size: 14
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    padding: 10,
                    titleFont: {
                        size: 14
                    },
                    bodyFont: {
                        size: 14
                    },
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value}명 (${percentage}%)`;
                        }
                    }
                }
            },
            animation: {
                animateScale: true,
                animateRotate: true,
                duration: 1500,
                easing: 'easeOutCirc'
            }
        };
        
        // 중앙에 텍스트 표시를 위한 플러그인
        const centerTextPlugin = {
            id: 'centerText',
            beforeDraw: function(chart) {
                const width = chart.width;
                const height = chart.height;
                const ctx = chart.ctx;
                
                ctx.restore();
                const fontSize = (height / 114).toFixed(2);
                ctx.font = fontSize + "em sans-serif";
                ctx.textBaseline = "middle";
                ctx.fillStyle = "#333";
                
                const text = `${completionRate}%`;
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;
                
                ctx.fillText(text, textX, textY);
                ctx.save();
            }
        };
        
        // 도넛 차트 생성
        new Chart(progressCtx, {
            type: 'doughnut',
            data: donutChartData,
            options: donutChartOptions,
            plugins: [centerTextPlugin]
        });
    }
});
</script>

<style>
/* 페이지 전체에 대한 기본 설정 */
html, body {
    max-width: 100%;
    overflow-x: hidden;
}

/* 메인 컨테이너 스타일 */
.main-container {
    padding: 2rem;
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    overflow-x: hidden;
    box-sizing: border-box;
}

/* 모든 자식 요소에 box-sizing 적용 */
.main-container * {
    box-sizing: border-box;
}

/* 대시보드 컨테이너 */
.dashboard-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

/* 통계 그리드 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    width: 100%;
    max-width: 100%;
}

/* 통계 요약 카드 */
.stat-summary-card {
    background: linear-gradient(145deg, #ffffff, #f8faff);
    border-radius: 16px;
    padding: 1.8rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.8);
    text-align: center;
    width: 100%;
    max-width: 100%;
    word-break: break-word;
}

/* 차트 관련 요소들 */
.progress-chart-container {
    width: 100%;
    max-width: 100%;
}

.chart-wrapper {
    position: relative;
    width: 100%;
    max-width: 100%;
    height: 300px;
    overflow: hidden;
}

/* 좌측 차트 대시보드 */
.dashboard-chart {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

/* 나머지 스타일은 유지 */
.welcome-section {
    text-align: center;
    margin-bottom: 3rem;
    background: linear-gradient(145deg, #ffffff, #f9f9f9);
    padding: 2.5rem 1.5rem;
    border-radius: 20px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(240, 240, 240, 0.8);
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(74,144,226,0.03) 0%, rgba(255,255,255,0) 70%);
    z-index: -1; /* z-index를 음수값으로 변경하여 다른 요소들보다 아래에 위치하도록 함 */
    pointer-events: none; /* 마우스 이벤트를 통과시키도록 설정 */
}

.welcome-title {
    font-size: 2.5rem;
    background: linear-gradient(90deg, #2c3e50, #4a90e2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1.2rem;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.btn-register, .btn-reregister {
    border: none;
    border-radius: 50px;
    padding: 0.9rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    margin: 1rem 0.7rem;
    display: inline-flex;
    align-items: center;
    gap: 0.7rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.btn-register::before, .btn-reregister::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2));
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: -1;
}

.btn-register:hover, .btn-reregister:hover {
    transform: translateY(-3px);
}

.btn-register:hover::before, .btn-reregister:hover::before {
    transform: translateX(0);
}

.btn-register {
    background: linear-gradient(135deg, #4CAF50, #3d9140);
    color: white;
}

.btn-register:hover {
    box-shadow: 0 6px 15px rgba(76, 175, 80, 0.4);
}

.btn-reregister {
    background: linear-gradient(135deg, #ff6666, #e55151);
    color: white;
}

.btn-reregister:hover {
    box-shadow: 0 6px 15px rgba(255, 102, 102, 0.4);
}

/* 통계 섹션 스타일 */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.stat-card {
    position: relative;
    background: linear-gradient(145deg, #ffffff, #f5f7fa);
    border-radius: 16px;
    padding: 1.8rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.6);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 25px rgba(0, 0, 0, 0.12);
}

.stat-card:hover::before {
    transform: translateX(100%);
}

.stat-icon {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    padding: 1.2rem;
    border-radius: 12px;
    margin-right: 1.2rem;
    box-shadow: 0 6px 10px rgba(74, 144, 226, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-icon i {
    font-size: 1.8rem;
    color: #ffffff;
}

.stat-content {
    flex-grow: 1;
}

.stat-content h3 {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(90deg, #2c3e50, #4a90e2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0;
    line-height: 1.2;
}

.tooltip {
    position: absolute;
    top: 12px;
    right: 12px;
    z-index: 5;
}

.tooltip i {
    color: #9d9d9d;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.8);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tooltip i:hover {
    color: #4a90e2;
    transform: scale(1.1);
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 220px;
    background: linear-gradient(145deg, #2c3e50, #34495e);
    color: #fff;
    text-align: center;
    border-radius: 10px;
    padding: 10px 15px;
    position: absolute;
    z-index: 10;
    top: -5px;
    right: 130%;
    font-size: 0.9rem;
    opacity: 0;
    transition: opacity 0.3s, transform 0.3s;
    transform: translateY(10px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    line-height: 1.5;
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 100%;
    margin-top: -5px;
    border-width: 6px;
    border-style: solid;
    border-color: transparent transparent transparent #34495e;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
}

/* 차트 컨테이너 스타일 */
.chart-container {
    grid-column: 1 / -1;
    width: 100%;
    min-height: 400px;
    margin-bottom: 2rem;
    background: linear-gradient(145deg, #ffffff, #f9f9f9);
    border-radius: 20px;
    padding: 1.8rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.8);
    position: relative;
    overflow: hidden;
}

.progress-chart-container {
    grid-column: 1 / -1;
    width: 100%;
    margin-top: 2rem;
    background: linear-gradient(145deg, #ffffff, #f9f9f9);
    border-radius: 20px;
    padding: 2.2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.8);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.chart-container::before, .progress-chart-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
    opacity: 0.3;
    z-index: 1;
    pointer-events: none;
}

.progress-chart-container h3 {
    font-size: 1.7rem;
    background: linear-gradient(90deg, #2c3e50, #4a90e2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1.8rem;
    font-weight: 700;
    position: relative;
    display: inline-block;
}

.progress-chart-container h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 10%;
    width: 80%;
    height: 3px;
    background: linear-gradient(90deg, rgba(74, 144, 226, 0.3), rgba(74, 144, 226, 0.7), rgba(74, 144, 226, 0.3));
    border-radius: 2px;
}

.chart-wrapper {
    position: relative;
    width: 100%;
    height: 320px;
    z-index: 2;
}

/* 그룹 섹션 스타일 */
.groups-section {
    margin-top: 3rem;
    background: linear-gradient(145deg, #ffffff, #f9f9f9);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(240, 240, 240, 0.8);
}

.groups-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(74,144,226,0.03) 0%, rgba(255,255,255,0) 70%);
    z-index: 0;
}

.section-title {
    font-size: 1.7rem;
    margin-bottom: 2.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #eef2f7;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    position: relative;
}

.section-title i {
    color: #4a90e2;
    font-size: 1.5rem;
    background: #f0f7ff;
    padding: 0.6rem;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(74, 144, 226, 0.2);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, #4a90e2, rgba(74, 144, 226, 0.3));
    border-radius: 2px;
}

.groups-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.8rem;
    padding: 1rem 0.5rem;
    position: relative;
    z-index: 1;
}

/* 학년/부서 카드 스타일 */
.grade-card {
    background: linear-gradient(145deg, #ffffff, #f9f9f9);
    border-radius: 16px;
    padding: 1.8rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(240, 240, 240, 0.8);
}

.grade-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: 0;
}

.grade-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.grade-card:hover::before {
    transform: translateX(100%);
}

.grade-card h4 {
    color: #2c3e50;
    font-size: 1.4rem;
    margin-bottom: 1.8rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding-bottom: 0.8rem;
    border-bottom: 2px solid #f0f4ff;
    position: relative;
}

.grade-card h4 i {
    color: #4a90e2;
    font-size: 1.5rem;
    background: #f0f7ff;
    padding: 0.6rem;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(74, 144, 226, 0.1);
}

/* 반 목록 스타일 */
.class-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.2rem;
}

.class-info-card {
    background: linear-gradient(145deg, #ffffff, #fafcff);
    border: 1px solid #eef2f7;
    border-radius: 12px;
    padding: 1.2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.class-info-card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    transform: translateY(-5px);
    border-color: #4a90e2;
}

.class-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.class-number {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.student-count {
    font-size: 1rem;
    color: #666;
    background: #f5f8ff;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    display: inline-block;
}

.view-button {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 0.8rem 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.6rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(74, 144, 226, 0.2);
    position: relative;
    overflow: hidden;
}

.view-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: 0;
}

.view-button:hover {
    background: linear-gradient(135deg, #357abd, #2a6daf);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(74, 144, 226, 0.3);
}

.view-button:hover::before {
    transform: translateX(100%);
}

.view-button i {
    font-size: 1rem;
    position: relative;
    z-index: 1;
}

.view-button span {
    font-size: 1rem;
    position: relative;
    z-index: 1;
}

/* 부서 카드 스타일 */
.dept-card {
    padding: 0;
    overflow: hidden;
}

.dept-info-card {
    background: linear-gradient(145deg, #ffffff, #fafcff);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(240, 240, 240, 0.8);
    position: relative;
    overflow: hidden;
}

.dept-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: 0;
}

.dept-info-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.dept-info-card:hover::before {
    transform: translateX(100%);
}

.dept-header {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
}

.dept-header i {
    font-size: 1.6rem;
    color: #4a90e2;
    background: #f0f7ff;
    padding: 1rem;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.15);
}

.dept-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.dept-name {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
}

.employee-count {
    font-size: 1rem;
    color: #666;
    background: #f5f8ff;
    padding: 0.4rem 0.9rem;
    border-radius: 20px;
    display: inline-block;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.view-dept-button {
    width: 100%;
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.7rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.2);
    margin-top: 1.5rem;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.view-dept-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: -1;
}

.view-dept-button:hover {
    background: linear-gradient(135deg, #357abd, #2a6daf);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(74, 144, 226, 0.3);
}

.view-dept-button:hover::before {
    transform: translateX(100%);
}

.view-dept-button i {
    font-size: 1.1rem;
}

/* 모달 스타일 개선 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1000;
    overflow-y: auto;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(145deg, #ffffff, #f9f9f9);
    max-width: 800px;
    width: 90%;
    margin: 5% auto;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    position: relative;
    animation: modalFade 0.4s ease-out;
    border: 1px solid rgba(240, 240, 240, 0.8);
}

@keyframes modalFade {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close-button {
    position: absolute;
    top: 1.2rem;
    right: 1.8rem;
    font-size: 2.2rem;
    color: #aaa;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-button:hover {
    color: #333;
    background: rgba(0, 0, 0, 0.05);
    transform: rotate(90deg);
}

.modal h2 {
    margin-top: 0;
    background: linear-gradient(90deg, #2c3e50, #4a90e2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 2rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.modal h4 {
    color: #666;
    font-weight: 400;
    font-size: 1.1rem;
    margin-top: 0;
    margin-bottom: 1.5rem;
}

.modal hr {
    border: 0;
    height: 2px;
    background: linear-gradient(to right, #f0f4ff, #4a90e2, #f0f4ff);
    margin: 1.5rem 0;
}

/* 사용자 카드 디자인 개선 */
.user-card {
    display: flex;
    align-items: center;
    padding: 1.2rem;
    margin-bottom: 1rem;
    background: linear-gradient(145deg, #ffffff, #fafcff);
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(240, 240, 240, 0.8);
    position: relative;
    overflow: hidden;
}

.user-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: 0;
}

.user-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.user-card:hover::before {
    transform: translateX(100%);
}

.s-user-info {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    width: 100%;
    position: relative;
    z-index: 1;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4a90e2, #357abd);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 10px rgba(74, 144, 226, 0.2);
}

.user-avatar i {
    color: white;
    font-size: 1.5rem;
}

.user-details {
    flex-grow: 1;
}

.user-name {
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    font-size: 1.2rem;
}

.user-class {
    color: #666;
    font-size: 1rem;
    margin: 0.3rem 0 0;
    padding: 0.3rem 0.8rem;
    background: #f5f8ff;
    border-radius: 20px;
    display: inline-block;
}

/* 검사 진행 현황 대시보드 스타일 */
.stats-overview {
    margin-bottom: 3rem;
}

.dashboard-title {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(90deg, #2c3e50, #4a90e2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1.5rem;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;
}

.dashboard-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 3px;
    background: linear-gradient(90deg, rgba(74, 144, 226, 0.3), rgba(74, 144, 226, 0.7), rgba(74, 144, 226, 0.3));
    border-radius: 2px;
}

.dashboard-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

.dashboard-chart {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

.dashboard-chart .progress-chart-container {
    height: 100%;
    margin-top: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.dashboard-chart .chart-wrapper {
    height: 280px;
}

.progress-legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-color {
    width: 15px;
    height: 15px;
    border-radius: 50%;
}

.legend-text {
    font-size: 1rem;
    color: #555;
    font-weight: 500;
}

/* 통계 그리드 스타일 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
    max-width: 100%; /* 부모 요소의 너비를 넘지 않도록 제한 */
}

/* 카드 요소에 너비 제한 추가 */
.stat-summary-card {
    background: linear-gradient(145deg, #ffffff, #f8faff);
    border-radius: 16px;
    padding: 1.8rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.8);
    text-align: center;
    max-width: 100%; /* 부모 영역을 넘지 않도록 제한 */
    word-break: break-word; /* 단어가 길어도 줄바꿈 처리 */
}

.stat-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: 0;
}

.stat-summary-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.stat-summary-card:hover::before {
    transform: translateX(100%);
}

.stat-summary-card[data-type="members"] .stat-icon-large {
    background: linear-gradient(135deg, #4a90e2, #357abd);
}

.stat-summary-card[data-type="body"] .stat-icon-large {
    background: linear-gradient(135deg, #4CAF50, #3d9140);
}

.stat-summary-card[data-type="gait"] .stat-icon-large {
    background: linear-gradient(135deg, #9C27B0, #7B1FA2);
}

.stat-summary-card[data-type="month"] .stat-icon-large {
    background: linear-gradient(135deg, #FF9800, #F57C00);
}

.stat-summary-card[data-type="pending"] .stat-icon-large {
    background: linear-gradient(135deg, #F44336, #D32F2F);
}

.stat-icon-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.2rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.stat-icon-large i {
    font-size: 2.5rem;
    color: white;
}

.stat-content-large {
    position: relative;
    z-index: 1;
}

.stat-content-large h3 {
    font-size: 1.2rem;
    color: #555;
    margin-bottom: 0.8rem;
    font-weight: 600;
}

.stat-number-large {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(90deg, #2c3e50, #4a90e2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 0 0.8rem;
    line-height: 1.2;
    display: flex;
    align-items: baseline;
    justify-content: center;
}

.stat-number-large span {
    font-size: 1.2rem;
    margin-left: 0.3rem;
}

.stat-desc {
    color: #777;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 반응형 디자인 강화 */
@media (max-width: 1024px) {
    .dashboard-container {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-title {
        font-size: 1.8rem;
    }
    
    .stat-summary-card {
        padding: 1.5rem;
    }
    
    .stat-icon-large {
        width: 70px;
        height: 70px;
    }
    
    .stat-icon-large i {
        font-size: 2rem;
    }
    
    .stat-number-large {
        font-size: 2.2rem;
    }
}

@media (max-width: 576px) {
    .dashboard-title {
        font-size: 1.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-summary-card {
        flex-direction: row;
        text-align: left;
        align-items: center;
        padding: 1.2rem;
    }
    
    .stat-icon-large {
        width: 60px;
        height: 60px;
        margin-bottom: 0;
        margin-right: 1rem;
    }
    
    .stat-icon-large i {
        font-size: 1.8rem;
    }
    
    .stat-content-large {
        flex-grow: 1;
    }
    
    .stat-number-large {
        font-size: 1.8rem;
        justify-content: flex-start;
    }
}

#progressChart {
    width: 100%;
    
}
</style>
{% endblock %}