name: Django CI/CD with E2E Tests

on:
  push:
    branches: ["feature-test"]
  # pull_request:
  #   branches: ["main"]

jobs:
  test-and-deploy:
    runs-on: ubuntu-latest
    strategy:
      max-parallel: 4
      matrix:
        python-version: [3.12]

    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: ⚙️ Setup Environment Variables
        run: |
          # .env 파일에 환경 변수 자동 세팅
          echo "DEV_POSTGRES_DB_NAME=${{ secrets.DEV_POSTGRES_DB_NAME }}" >> .env
          echo "DEV_POSTGRES_USER=${{ secrets.DEV_POSTGRES_USER }}" >> .env
          echo "DEV_POSTGRES_PASSWORD=${{ secrets.DEV_POSTGRES_PASSWORD }}" >> .env
          echo "DEV_POSTGRES_HOST=${{ secrets.DEV_POSTGRES_HOST }}" >> .env
          echo "DEV_POSTGRES_PORT=${{ secrets.DEV_POSTGRES_PORT }}" >> .env
          echo "POSTGRES_DB_NAME=${{ secrets.POSTGRES_DB_NAME }}" >> .env
          echo "POSTGRES_USER=${{ secrets.POSTGRES_USER }}" >> .env
          echo "POSTGRES_PASSWORD=${{ secrets.POSTGRES_PASSWORD }}" >> .env
          echo "POSTGRES_HOST=${{ secrets.POSTGRES_HOST }}" >> .env
          echo "POSTGRES_PORT=${{ secrets.POSTGRES_PORT }}" >> .env
          echo "EMAIL_HOST=${{ secrets.EMAIL_HOST }}" >> .env
          echo "EMAIL_USER=${{ secrets.EMAIL_USER }}" >> .env
          echo "EMAIL_PASSWORD=${{ secrets.EMAIL_PASSWORD }}" >> .env
          echo "DEFAULT_PASSWORD=${{ secrets.DEFAULT_PASSWORD }}" >> .env
          echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}" >> .env
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> .env
          echo "AWS_STORAGE_BUCKET_NAME=${{ secrets.AWS_STORAGE_BUCKET_NAME }}" >> .env
          echo "AWS_S3_REGION_NAME=${{ secrets.AWS_S3_REGION_NAME }}" >> .env
          echo "SECRET_KEY=\"${{ secrets.SECRET_KEY }}\"" >> .env
          echo "ENVIRONMENT=dev" >> .env

      - name: 🐍 Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v3
        with:
          python-version: ${{ matrix.python-version }}

      - name: 📦 Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          mkdir -p logs

      - name: 🗃️ Database Migration & Setup
        run: |
          rm -rf analysis/migrations
          python manage.py makemigrations analysis
          python manage.py migrate

      - name: 🧪 Run All Django Tests (Beautiful Output)
        run: |
          python manage.py test -v 2 --noinput --keepdb || {
            echo "❌ Django Tests Failed!"
            exit 1
          }

      - name: 📊 Test Summary
        if: always()
        run: |
          echo "🎉 ALL E2E TESTS COMPLETED SUCCESSFULLY! 🎉"
          echo "=============================================="
          echo "✅ Mobile Platform Tests: PASSED"
          echo "✅ Kiosk Platform Tests: PASSED"
          echo "✅ AOS Platform Tests: PASSED"
          echo "✅ Cross-Platform Integration: PASSED"
          echo "✅ Error Handling & Edge Cases: PASSED"
          echo "✅ Performance & Load Tests: PASSED"
          echo "=============================================="
          echo "🚀 Ready for deployment!"

      - name: 🚀 Deploy to Development Server
        if: success()
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DEV_SSH_HOST }}
          username: ${{ secrets.DEV_SSH_USERNAME }}
          password: ${{ secrets.SSH_PASSWORD }}
          port: ${{ secrets.DEV_SSH_PORT }}
          script: |
            source ~/.zshrc
            docker exec bodyscanner_dev_server bash -c "
              git pull origin feature-test &&
              pip install --upgrade pip &&
              pip install -r requirements.txt &&
              rm -rf analysis/migrations &&
              python manage.py makemigrations analysis &&
              python manage.py migrate &&
              bash ./restart_django.sh
            "
            echo "🎉 DEPLOYMENT SUCCESSFUL!"