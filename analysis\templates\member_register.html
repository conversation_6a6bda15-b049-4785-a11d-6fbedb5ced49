{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}Main Page{% endblock %}

{% block content %}

<!-- AG Grid 및 XLSX 라이브러리 로드 -->
<script src="https://cdn.jsdelivr.net/npm/ag-grid-community/dist/ag-grid-community.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.0/xlsx.full.min.js"></script>

<style>
    .dashboard-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
        padding-bottom: 1rem;
        display: flex;
        flex-direction: column;
        text-align: center;
    }
    
    .function-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
        padding-top: 0;
        display: flex;
        flex-direction: column;
        text-align: center;
    }
    
    .function-section {
        border-radius: 15px;
        border: 1px solid #e1e4e8;
        background: #fff;
        width: 90%;
        margin: 0rem auto;
        padding: 2rem;
    }
    
    .info_container {
        border-radius: 15px;
        border: 1px solid #e1e4e8;
        background: #fff;
        width: 90%;
        margin: 2rem auto;
        padding: 2rem;
    }
    
    .excel-preview {
        margin: 1.5rem 0;
        border: 1px solid #e1e4e8;
        border-radius: 4px;
    }
    
    .excel-table {
        width: 100%;
        border-collapse: collapse;
        background: #fff;
        font-size: 0.9rem;
    }
    
    .excel-table th {
        background: #f1f3f4;
        border: 1px solid #e1e4e8;
        padding: 8px 12px;
        text-align: center;
        font-weight: 600;
    }
    
    .excel-table td {
        border: 1px solid #e1e4e8;
        padding: 8px 12px;
        text-align: center;
    }
    
    .excel-table tr:nth-child(even) {
        background: #f8f9fa;
    }
    
    .excel-table tr:hover {
        background: #f5f5f5;
    }
    
    .info_container > h2, 
    .info_container > p {  /* p 선택자를 더 구체적으로 지정 */
        width: 100%;  /* 부모 컨테이너 기준으로 설정 */
        margin: 0 auto;  
        margin-bottom: 10px;
    }
    
    .welcome-section {
        text-align: center;
        margin-bottom: 3rem;
    }
    
    .content-grid {
        padding: 2rem;
        padding-top: 0;
        margin-top: 2rem;
    }
    
    .card-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .feature-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .card-header {
        background: linear-gradient(135deg, #4CAF50, #45a049);
        color: white;
        padding: 1.5rem;
        text-align: center;
    }
    
    .card-header i {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    
    .card-header h4 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
    }
    
    .card-body {
        padding: 2rem;
        text-align: center;
    }
    
    .download-btn {
        display: inline-block;
        padding: 0.8rem 1.5rem;
        background: #4CAF50;
        color: white;
        border-radius: 25px;
        text-decoration: none;
        transition: background 0.3s ease;
        margin-top: 1rem;
    }
    
    .download-btn:hover {
        background: #45a049;
    }
    
    .dropzone {
        border: 2px dashed #4CAF50;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }
    
    .dropzone:hover {
        border-color: #45a049;
        background: #f0f2f0;
    }
    
    .dropzone i {
        font-size: 2.5rem;
        color: #4CAF50;
        margin-bottom: 1rem;
    }
    
    .dropzone p {
        color: #666;
        margin: 0;
    }
    
    .upload-card .card-body {
        padding: 1.5rem;
    }
    
    .result-section {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .table-wrapper {
        margin: 1rem 0;
        border-radius: 8px;
        overflow: auto; /* 수평 스크롤 가능하게 설정 */
    }
    
    table {
        width: 100%;
        border-collapse: collapse;
        background-color: #fff;
        min-width: 600px; /* 최소 너비 설정 */
        table-layout: auto; /* 자동 레이아웃 설정 */
    }
    
    tbody {
        overflow: hidden;
    }
    
    th, td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }
    
    th {
        background-color: #f8f9fa;
        font-weight: bold;
        color: #333;
    }
    
    tr:hover {
        background-color: #f1f1f1; /* 행 hover 시 색상 변경 */
    }
    
    .pagination {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .page-btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 5px;
        background: #4CAF50;
        color: white;
        cursor: pointer;
        transition: background 0.3s ease, transform 0.2s ease;
    }
    
    .page-btn:hover {
        background: #45a049;
        transform: scale(1.05); /* 버튼 hover 시 약간 커짐 */
    }
    
    .page-btn:disabled {
        background: #ccc; /* 비활성화된 버튼 색상 */
        cursor: not-allowed;
    }
    
    /* 페이지 트랜지션 스타일 */
    .page-transition-wrapper {
        animation: fadeIn 0.3s ease-in;
    }
    
    #spinnerContainer > p {
        color: #333;
     }
    
    @media (max-width: 768px) {
        .card-container {
            grid-template-columns: 1fr;
        }
        
        .feature-card {
            margin: 0 1rem;
        }
    
        table {
            width: 100%; /* 작은 화면에서 테이블 너비를 100%로 설정 */
            font-size: 0.9rem; /* 폰트 크기 조정 */
        }
    
        th, td {
            padding: 0.5rem; /* 패딩 조정 */
        }
    }
    
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    /* 알림 스타일 */
    .alert {
        padding: 15px;
        margin: 20px 0;
        border-radius: 8px;
    }
    
    .alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }
    
    .alert-error {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }
    
    .alert-info {
        background-color: #e2e3e5;
        border-color: #d6d8db;
        color: #383d41;
    }
    
    .text-danger {
        color: red; /* 빨간색으로 하이라이트 */
        font-weight: bold;
    }
    
    .text-danger2 {
        color: #ff8686; /* 빨간색으로 하이라이트 */
        font-weight: bold;
    }
    
    /* 스피너 스타일 */
    .spinner-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        z-index: 1000;
    }
    
    .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #4CAF50;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* 파일 정보 섹션 스타일 추가 */
    .info-section {
        margin-top: 2rem;
        padding: 1rem;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        display: none;
    }
    
    .file-info {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .confirm-btn {
        padding: 0.8rem 1.5rem;
        background: #4CAF50;
        color: white;
        border: none;
        border-radius: 25px;
        cursor: pointer;
        transition: background 0.3s ease;
    }
    
    .confirm-btn:hover {
        background: #45a049;
    }
    
    /* 버튼 그룹 스타일 추가 */
    .button-group {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .reset-btn {
        padding: 0.8rem 1.5rem;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 25px;
        cursor: pointer;
        transition: background 0.3s ease;
    }
    
    .reset-btn:hover {
        background: #c82333;
    }
    
    .status-new {
        color: green;
        font-weight: bold;
    }
    .status-update {
        color: blue;
        font-weight: bold;
    }
    
    .summary-info {
        display: flex;
        justify-content: space-around; /* 요소 간격을 균등하게 배치 */
        margin-top: 1rem;
        padding: 1rem;
        background-color: #f8f9fa; /* 배경색 추가 */
        border-radius: 8px; /* 모서리 둥글게 */
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 그림자 추가 */
    }
    
    .summary-info span {
        font-size: 1rem; /* 글자 크기 조정 */
        color: #333; /* 글자 색상 */
    }
    
    /* 신규 등록 및 기존 유저 갱신 상태 강조 */
    .new-member-status strong {
        color: green; /* 신규 등록 색상 */
    }
    
    .existing-member-status strong {
        color: blue; /* 기존 유저 갱신 색상 */
    }
    
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }
    
    .modal-content {
        background: #ffffff;
        padding: 2rem;
        border-radius: 12px;
        width: 90%;
        max-width: 450px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        animation: slideDown 0.3s ease-out;
    }
    
    .modal-content h2 {
        color: #2c3e50;
        margin-bottom: 1.5rem;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .modal-content form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .modal-content form div {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .modal-content label {
        color: #4a5568;
        font-size: 0.9rem;
        font-weight: 500;
    }
    
    .modal-content input {
        padding: 0.8rem;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        font-size: 1rem;
        transition: border-color 0.2s;
    }
    
    .modal-content input:focus {
        outline: none;
        border-color: #4CAF50;
        box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    }
    
    .close {
        color: #aaa;
        float: right; /* 오른쪽 정렬 */
        font-size: 28px;
        font-weight: bold;
    }
    
    .close:hover,
    .close:focus {
        color: black;
        text-decoration: none;
        cursor: pointer;
    }
    
    .add-btn {
        display: inline-block;
        padding: 0.8rem 1.5rem;
        background: #4CAF50;
        color: white;
        border-radius: 25px;
        text-decoration: none;
        transition: background 0.3s ease;
        margin-top: 1rem;
        background: #007bff;
    }
    
    .add-btn:hover {
        background: #458ad4;
    }
    
    @keyframes slideDown {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    
    @media (max-width: 480px) {
        .modal-content {
            width: 95%;
            padding: 1.5rem;
        }
    }
    </style>

<div class="page-transition-wrapper">
<div class="dashboard-container">
    {% if user_type == 'S' %} <!-- 소속이 학교 일 경우 학교 아이콘 -->
                <i class="fas fa-school" style="font-size: 3rem; color: #111;"></i>
                <h2 class="welcome-title">{{ orgName }}</h2>
                {% else %} <!-- 소속이 기업일 경우 기업(기관) 아이콘 -->
                <i class="fas fa-building" style="font-size: 3rem; color: #111;"></i>
                <h2 class="welcome-title">{{ orgName }}</h2>
                {% endif %}
    
    <!-- 성공/실패 알림 -->
    <div class="alert alert-success" style="display: none;">
        <strong>성공!</strong> 사용자를 성공적으로 등록하였습니다.
    </div>
    <div class="alert alert-error" style="display: none;"></div>

    <!-- 사용법 섹션 -->
     <div class="info_container">
        <h2><i class="fas fa-info-circle"></i> 사용법</h2>
        <p id="info_text">아래의 템플릿을 다운로드하여 엑셀 파일을 작성한 후 업로드해주세요.</p>
                <!-- 개인정보 수집 동의 섹션 추가 -->
                <div class="consent-section" style="display: none">
                    <label>
                        <input type="checkbox" id="consentCheckbox"> 개인정보 수집 동의(필수)
                    </label>
                    <button id="consentDetailBtn" class="add-btn">자세히보기</button>
                </div>
                <script>
                        document.querySelector('#consentDetailBtn').addEventListener('click', function() {
                                    window.open('{% url "policy" %}', '_blank');
                        });
                </script>
        {% if user_type == 'S' %}
        <div class="excel-preview">
            <table class="excel-table">
                <thead>
                    <tr>
                        <th>학년</th>
                        <th>반</th>
                        <th>이름</th>
                        <th>전화번호</th>
                        <th>생년월일</th>
                        <th>성별</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>2</td>
                        <td>홍길동</td>
                        <td>010-0000-0000</td>
                        <td>1990</td>
                        <td>M</td>
                    </tr>
                    <tr>
                        <td>1</td>
                        <td>2</td>
                        <td>김순이</td>
                        <td>010-1111-1111</td>
                        <td>1992</td>
                        <td>F</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    {% elif user_type == 'O' %}
    <div class="excel-preview">
        <table class="excel-table">
            <thead>
                <tr>
                    <th>부서명</th>
                    <th>이름</th>
                    <th>전화번호</th>
                    <th>생년월일</th>
                    <th>성별</th>
                </tr>
            </thead>
            <tbody>
                    <td>영업부</td>
                    <td>홍길동</td>
                    <td>010-0000-0000</td>
                    <td>1990</td>
                    <td>M</td>
                </tr>
                <tr>
                    <td>인사부</td>
                    <td>김순이</td>
                    <td>010-1111-1111</td>
                    <td>1992</td>
                    <td>F</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
</div>
    {% endif %}


     <!-- 첫 번째 모달 (입력 폼) -->
    <div id="inputModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close" id="closeInputModal">&times;</span>
            <h2><i class="fas fa-user-plus"></i> 수동 추가</h2>
            <form id="manualEntryForm">
                <div>
                    <label for="name">이름:</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div>
                    <label for="phone">전화번호:</label>
                    <input type="text" id="phone" name="phone" required>
                </div>
                <div>
                    <label for="dob">생년월일: 4자리</label>
                    <input type="number" id="dob" name="dob" required>
                </div>
                <div>
                    <label for="gender">성별:</label>
                    <select id="gender" name="gender" required>
                        <option value="M">남성</option>
                        <option value="F">여성</option>
                    </select>
                </div>
                {% if user_type == 'S' %}
                <div id="departmentContainer">
                    <label for="department">부서/학급:</label>
                    <input type="text" id="department" name="department" placeholder="예: 6학년 1반, 교무실, 교장실 등" required>
                    <small>(학생은 '학년 반' 형식으로 입력해주세요. 예: 6학년 1반, 2학년 3반 )</small>
                </div>
                {% elif user_type == 'O' %}
                <div>
                    <label for="department">부서명:</label>
                    <input type="text" id="department" name="department" required>
                </div>
                {% endif %}
                <button type="button" id="submitManualEntryBtn" class="add-btn">추가</button>
            </form>
        </div>
    </div>

    <!-- 저장 확인 모달창 -->
    <div id="confirmDiv" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-button" id="closeModal">&times;</span>
            <p id="manualEntryInfo">내용</p>
            <div class="modal-buttons">
                <button id="saveButton">저장</button>
                <button id="cancelButton">취소</button>
            </div>
        </div>
    </div>


     <!-- 템플릿 다운로드 섹션 -->
    <div class="content-grid" id="initialContent">
        <div class="card-container">
            {% if user_type == 'S' %}
            <div class="feature-card">
                <div class="card-header">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <h4>학교 구성원 등록용</h4>
                </div>
                <div class="card-body">
                    {% comment %} <p>학교 구성원 등록용</p> {% endcomment %}
                    <br/>
                    <a href="{% static '학교용 템플릿.xlsx' %}" download class="download-btn">
                        다운로드 <i class="fas fa-arrow-down"></i>
                    </a>
                    <button type="button" id="openInputModal" class="add-btn">수동 추가<i class="fas fa-add"></i></button> <!-- 수동 추가 버튼 -->
                </div>
            </div>
            {% elif user_type == 'O' %}
            <div class="feature-card">
                <div class="card-header">
                    <i class="fas fa-building"></i>
                    <h4>기업/기관 구성원 등록용</h4>
                </div>
                <div class="card-body">
                    {% comment %} <p>기업/기관 구성원 등록용</p> {% endcomment %}
                    <br/>
                    <a href="{% static '일반기관용 템플릿.xlsx' %}" download class="download-btn">
                        다운로드 <i class="fas fa-arrow-down"></i>
                    </a>
                    <button type="button" id="openInputModal" class="add-btn">수동 추가<i class="fas fa-add"></i></button> <!-- 수동 추가 버튼 -->
                </div>
            </div>
            {% endif %}

            <!-- 파일 업로드 카드 -->
            <div class="feature-card upload-card">
                <div class="card-header">
                    <i class="fas fa-upload"></i>
                    <h4>구성원 등록</h4>
                </div>
                <div class="card-body">
                    <form id="uploadForm" method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="dropzone" id="dragDropArea" onclick="document.getElementById('fileInput').click();">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>파일을 드래그하거나 클릭하여 업로드하세요</p>
                            <input type="file" name="file" id="fileInput" accept=".xlsx" style="display: none;" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="function-container">
        <!-- 파일 정보 섹션 수정 -->
    <div id="fileinfoSection" class="function-section" style="display: none;">
        <div class="file-info">
            <i class="fas fa-file-excel"></i>
            <span id="uploadedFileName">파일 이름</span>
        </div>
        <div class="button-group">
            <button id="resetButton" class="reset-btn">처음부터</button>
            <button id="confirmButton" class="confirm-btn">최종 저장</button>
        </div>
    </div>
    </div>
    

    <!-- 업로드 결과 테이블 -->
    <div id="tableContainer" class="result-section" style="display: none;">
        <div class="result-header">
            <h3><i class="fas fa-list"></i> 등록 현황</h3>
            <div class="summary-info">
                <span class="total-count">총 <strong id="totalCount">0</strong>명</span>
                <span class="new-member-status">신규 등록: <strong id="newMemberCount">0</strong>명</span>
                <span class="existing-member-status">기존 유저 갱신: <strong id="existingMemberCount">0</strong>명</span>
            </div>
        </div>
        
        <!-- AG-Grid 컨테이너 추가 -->
        <div id="myGrid" class="ag-theme-alpine" style="width: 100%; height: 500px; margin-top: 10px;"></div>
    </div>

    <!-- 로딩 스피너 -->
    <div id="spinnerContainer" class="spinner-container" style="display: none;">
        <div class="spinner"></div>
        <p>잠시만 기다려주세요.. 업로드 된 파일을 확인중입니다.</p>
    </div>
</div>
</div>

<script src="/static/js/ag-grid_locale.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('uploadForm');
    const fileInput = document.getElementById('fileInput');
    const dragDropArea = document.getElementById('dragDropArea');
    const tableContainer = document.getElementById('tableContainer');
    const spinnerContainer = document.getElementById('spinnerContainer');
    const successAlert = document.querySelector('.alert-success');
    const errorAlert = document.querySelector('.alert-error');
    const totalCountElement = document.getElementById('totalCount');
    
    let currentPage = 1;
    const rowsPerPage = 100; // 한 페이지당 표시할 행 수
    let uploadedData = null;
    let gridApi = null;

    // 드래그 앤 드롭 이벤트 핸들러
    dragDropArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        dragDropArea.classList.add('dragover');
    });

    dragDropArea.addEventListener('dragleave', () => {
        dragDropArea.classList.remove('dragover');
    });

    dragDropArea.addEventListener('drop', (e) => {
        e.preventDefault();
        dragDropArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileUpload(files[0]);
        }
    });

    // 파일 입력 변경 이벤트
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileUpload(e.target.files[0]);
        }
    });

    // 파일 업로드 처리 함수 수정
    function handleFileUpload(file) {
        if (!file.name.endsWith('.xlsx')) {
            showError('엑셀 파일(.xlsx)만 업로드 가능합니다.');
            return;
        }

        spinnerContainer.style.display = 'flex';
        const formData = new FormData();
        formData.append('file', file);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        // 초기 컨텐츠 숨기기
        document.getElementById('initialContent').style.display = 'none';

        // 미리보기 요청
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => 
            response.json().then(data => {
                if (!response.ok) {
                    throw new Error(data.error || '파일 업로드 중 오류가 발생했습니다.');
                }
                return data;
            })
        )
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            uploadedData = data;
            console.log(uploadedData);
            createAgGrid(data);
            document.getElementById('fileinfoSection').style.display = 'block';
            document.getElementById('uploadedFileName').textContent = file.name;
            document.querySelector('.excel-preview').style.display = 'none';
            document.getElementById('info_text').innerHTML = '등록 현황 확인 후 "<strong>최종 저장</strong>" 버튼을 클릭해주세요.';
            document.querySelector('.consent-section').style.display = 'block';
            hideError();
        })
        .catch(error => {
            console.error('Error:', error);
            showError(error.message);
            resetInterface();
            document.getElementById('initialContent').style.display = 'block';
        })
        .finally(() => {
            spinnerContainer.style.display = 'none';
        });
    }

    // AG-Grid 생성 함수
    function createAgGrid(data) {
        const { users, columns, new_member, existing_member } = data;
        
        console.log(columns);

        // 컬럼 정의 생성
        const columnDefs = columns.map(col => {
            const colDef = { 
                field: col, 
                headerName: col,
                sortable: true, 
                filter: true,
                resizable: true
            };
            
            // 성별 컬럼에 셀 에디터 추가
            if (col === '성별') {
                colDef.cellEditor = 'agSelectCellEditor';
                colDef.cellEditorParams = {
                    values: ['M', 'F']
                };
            }

            if (col === '상태') { // 해당 컬럼은 수정 불가
                colDef.editable = false;
            } else { // 나머지 컬럼은 수정 가능
                colDef.editable = true; // 나머지 컬럼은 수정 가능
            }
            
            // 상태 컬럼 서식 설정
            if (col === '상태') {
                colDef.cellStyle = params => {
                    if (params.value && params.value.startsWith('신규 등록')) {
                        return { color: 'green', fontWeight: 'bold' };
                    } else if (params.value && params.value.startsWith('기존 유저 갱신')) {
                        return { color: 'blue', fontWeight: 'bold' };
                    } else if (params.value && params.value.startsWith('이전 학교')) {
                        return { color: 'red', fontWeight: 'bold' };
                    } else if (params.value && params.value.startsWith('이전 소속')) {
                        return { color: '#ff8686', fontWeight: 'bold' };
                    }
                    return null;
                };
            }
            
            return colDef;
        });

        // 그리드 옵션
        const gridOptions = {
            columnDefs: columnDefs,
            rowData: users,
            pagination: true,
            paginationPageSize: rowsPerPage,
            localeText: AG_GRID_LOCALE_KR,
            defaultColDef: {
                editable: true,
                sortable: true,
                filter: true,
                resizable: true,
                minWidth: 100
            },
            onGridReady: function(params) {
                params.api.sizeColumnsToFit();
            },
            onFirstDataRendered: onFirstDataRendered
        };

        // 그리드 생성
        const eGridDiv = document.querySelector('#myGrid');
        gridApi = agGrid.createGrid(eGridDiv, gridOptions);
        
        // 회원 상태 업데이트
        updateMemberStatus(users.length, new_member, existing_member);
        
        // 테이블 컨테이너 표시
        tableContainer.style.display = 'block';
    }

    // 그리드 첫 렌더링 이벤트
    function onFirstDataRendered(params) {
        params.api.sizeColumnsToFit();
    }

    // 회원 상태 업데이트 함수
    function updateMemberStatus(totalCount, newMemberCount, existingMemberCount) {
        document.getElementById('totalCount').innerText = totalCount;
        document.getElementById('newMemberCount').innerText = newMemberCount;
        document.getElementById('existingMemberCount').innerText = existingMemberCount;
    }

    // 최종 저장 버튼 클릭 이벤트
    document.getElementById('confirmButton').addEventListener('click', () => {
        if (!uploadedData || !gridApi) {
            showError('업로드된 파일이 없습니다.');
            return;
        }

        if (!document.getElementById('consentCheckbox').checked) {
            showError('개인정보 수집 및 이용에 동의해주세요.');
            return;
        }

        spinnerContainer.style.display = 'flex';
        
        // AG-Grid에서 데이터 가져오기
        const allRowData = [];
        gridApi.forEachNode(node => {
            const data = {...node.data};
            // '상태' 속성 제외하고 전송 데이터 구성
            console.log(data);
            delete data['상태'];
            allRowData.push(data);
        });

        console.log(allRowData);

        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.json_to_sheet(allRowData);
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
    
        // 엑셀 파일로 변환
        const excelBuffer = XLSX.write(workbook, { type: 'array', bookType: 'xlsx' });
        const excelFile = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

        const formData = new FormData();
        formData.append('file', excelFile, 'updated_file.xlsx');
        formData.append('save', 'true');
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        // JSON 데이터를 서버로 전송
        fetch(window.location.href, {
            method: 'POST',
            body: formData,
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            const new_member = data.new_member;
            const existing_member = data.existing_member;
            showSuccess("기존 회원: " + existing_member + "명, 신규 회원: " + new_member + "명의 구성원이 추가되었습니다.");
            resetInterface();
        })
        .catch(error => {
            console.error(error);
            showError(error.message);
        })
        .finally(() => {
            spinnerContainer.style.display = 'none';
        });
    });

    // 에러 표시 함수 수정
    function showError(message) {
        const errorAlert = document.querySelector('.alert-error');
        errorAlert.textContent = message;
        errorAlert.style.display = 'block';
        document.querySelector('.alert-success').style.display = 'none';
        
        // 에러 메시지를 화면에 표시할 때 스크롤
        errorAlert.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // 성공 메시지 표시 함수
    function showSuccess(message) {
        successAlert.textContent = message;
        successAlert.style.display = 'block';
        errorAlert.style.display = 'none';
    }

    // 에러 숨기기 함수
    function hideError() {
        document.querySelector('.alert-error').style.display = 'none';
        document.querySelector('.alert-success').style.display = 'none';
    }

    // 인터페이스 초기화 함수 수정
    function resetInterface() {
        uploadedData = null;
        if (gridApi) {
            gridApi.setGridOption('rowData', []);
            gridApi.destroy();
        }

        fileInput.value = '';
        tableContainer.style.display = 'none';
        document.querySelector('.excel-preview').style.display = 'block';
        document.getElementById('fileinfoSection').style.display = 'none';
        document.getElementById('info_text').textContent = '아래의 템플릿을 다운로드하여 엑셀 파일을 작성한 후 업로드해주세요.';
        document.querySelector('#initialContent').style.display = 'block';
        document.querySelector('.consent-section').style.display = 'none';
    }

    // 처음부터 버튼 이벤트 리스너 수정
    document.getElementById('resetButton').addEventListener('click', () => {
        resetInterface();
    });

    document.getElementById('openInputModal').addEventListener('click', function() {
        openModal('input'); // 모달 열기 함수 호출
    });

    document.getElementById('closeModal').addEventListener('click', function() {
        closeModal('confirm'); // 모달 닫기 함수 호출
    });

    document.getElementById('cancelButton').addEventListener('click', function() {
        closeModal('confirm'); // 모달 닫기 함수 호출
    });

    document.getElementById('closeInputModal').addEventListener('click', function() {
        closeModal('input'); // 모달 닫기 함수 호출
    });

    // 모달 열기
    function openModal(type) {
        if (type === 'input') {
            document.getElementById('inputModal').style.display = 'block';
        } else if (type === 'confirm') {
            document.getElementById('confirmDiv').style.display = 'block';
        }
    }

    // 모달 닫기
    function closeModal(type) {
        if (type === 'input') {
            document.getElementById('inputModal').style.display = 'none';
        } else if (type === 'confirm') {
            document.getElementById('confirmDiv').style.display = 'none';
        }
    }

    // 모달 외부 클릭 시 닫기
    window.onclick = function(event) {
        const modal = document.getElementById('confirmDiv');
        const modal2 = document.getElementById('inputModal');
        if (event.target === modal) {
            closeModal('confirm');
        }
        if (event.target === modal2) {
            closeModal('input');
        }
    };

    const manualEntryBtn= document.getElementById('submitManualEntryBtn');
    manualEntryBtn.addEventListener('click', async function(e) {
        e.preventDefault();

        // 엑셀 파일 생성
        const workbook = XLSX.utils.book_new();
        
        const formInputs = new FormData(manualEntryForm);
        const data = {
            이름: formInputs.get('name'),
            전화번호: formInputs.get('phone'),
            생년월일: formInputs.get('dob'),
            성별: formInputs.get('gender'),
            {% if user_type == 'S' %}
            부서명: formInputs.get('department'),
            {% elif user_type == 'O' %}
            부서명: formInputs.get('department')
            {% endif %}
        };

        if (!data.이름 || !data.전화번호 || !data.생년월일 || !data.성별) {
            alert('정보를 입력해주세요.');
            return;
        }

        {% if user_type == 'S' %}
            if (!data.부서명) {
                alert('부서/학급을 입력해주세요.');
                return;
            }
        
        {% elif user_type == 'O' %}
        if (!data.부서명) {
            alert('부서명을 입력해주세요.');
            return;
        }
        {% endif %}



        // 워크시트 생성
        const worksheet = XLSX.utils.json_to_sheet([data]);
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
    
        // 엑셀 파일로 변환
        const excelBuffer = XLSX.write(workbook, { type: 'array', bookType: 'xlsx' });
        const excelFile = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    
        try {
            // 먼저 검증 요청 보내기
            const sendFormData = new FormData();
            sendFormData.append('file', excelFile, 'manual_entry.xlsx');
            
            const response = await fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: sendFormData
            });
    
            const result = await response.json();
            
            if (result.error) {
                throw new Error(result.error);
            }
    
            // 검증 데이터 표시 및 확인 요청
            var user = result.users;

            if (user.length === 1) { // 검증
                user = user[0];
                // 통과 메시지 표시
                document.getElementById('manualEntryInfo').textContent = `${user['이름']} 님의 정보가 확인되었습니다. 저장하시겠습니까?`;
                document.getElementById('confirmDiv').style.display = 'block';
            }

            document.getElementById('saveButton').onclick = async function() {
                // 저장 요청 보내기
                const saveFormData = new FormData();
                saveFormData.append('file', excelFile, 'manual_entry.xlsx');
                saveFormData.append('save', 'true');
                
                const saveResponse = await fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: saveFormData
                });
    
                const saveResult = await saveResponse.json();
                
                if (saveResult.error) {
                    throw new Error(saveResult.error);
                }
                
                showSuccess("사용자가 성공적으로 저장되었습니다.");
                resetManualEntryForm();
                closeModal('input');
                closeModal('confirm');
            }
        } catch (error) {
            showError(error.message);
        } 
    });

    // 수동 추가 폼 초기화 함수
    function resetManualEntryForm() {
        manualEntryForm.reset();
    }
});
</script>
{% endblock %}




