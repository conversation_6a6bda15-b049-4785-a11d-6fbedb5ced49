"""
Custom test runner for beautiful E2E test output in GitHub Actions
"""
import time
import sys
import os
from django.test.runner import <PERSON><PERSON><PERSON><PERSON>ner
from django.test import TestCase
from io import StringIO
import unittest

# Windows에서 이모지 지원을 위한 함수
def safe_print(text):
    """Windows에서 이모지를 안전하게 출력하는 함수"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 이모지를 ASCII 문자로 대체
        text = text.replace('📋', '[INFO]')
        text = text.replace('✅', '[PASS]')
        text = text.replace('❌', '[FAIL]')
        text = text.replace('🧪', '[TEST]')
        text = text.replace('⏭️', '[SKIP]')
        text = text.replace('🚀', '[START]')
        text = text.replace('🎉', '[DONE]')
        text = text.replace('⏱️', '[TIME]')
        print(text)


class ColoredTestResult:
    """테스트 결과에 색상과 이모지를 추가하는 클래스"""
    
    def __init__(self, stream, verbosity):
        self.stream = stream
        self.verbosity = verbosity
        self.test_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.error_count = 0
        self.start_time = None
        
    def start_test(self, test):
        self.test_count += 1
        if self.verbosity > 1:
            test_name = self._get_test_name(test)
            self.stream.write(f"  🧪 {test_name}... ")
            self.stream.flush()
        
    def add_success(self, test):
        self.success_count += 1
        if self.verbosity > 1:
            self.stream.write("✅ PASS\n")
            
    def add_error(self, test, err):
        self.error_count += 1
        if self.verbosity > 1:
            self.stream.write("❌ ERROR\n")
            
    def add_failure(self, test, err):
        self.failure_count += 1
        if self.verbosity > 1:
            self.stream.write("❌ FAIL\n")
            
    def add_skip(self, test, reason):
        if self.verbosity > 1:
            self.stream.write("⏭️ SKIP\n")
    
    def _get_test_name(self, test):
        """테스트 이름을 보기 좋게 포맷팅"""
        test_method = str(test).split(' ')[0]
        class_name = test.__class__.__name__
        
        # 테스트 메서드 이름을 읽기 쉽게 변환
        method_name = test_method.split('.')[-1]
        if method_name.startswith('test_'):
            method_name = method_name[5:]  # 'test_' 제거
        
        # 언더스코어를 공백으로 변경
        method_name = method_name.replace('_', ' ').title()
        
        return f"{class_name}: {method_name}"


class BeautifulTestRunner(DiscoverRunner):
    """GitHub Actions에서 보기 좋은 출력을 위한 커스텀 테스트 러너"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.start_time = None
        
    def setup_test_environment(self, **kwargs):
        super().setup_test_environment(**kwargs)
        safe_print("\n" + "="*60)
        safe_print("🚀 STARTING E2E TEST SUITE")
        safe_print("="*60)
        self.start_time = time.time()
        
    def teardown_test_environment(self, **kwargs):
        super().teardown_test_environment(**kwargs)
        if self.start_time:
            duration = time.time() - self.start_time
        
        safe_print(f"\n⏱️  Total execution time: {duration:.2f} seconds")
        safe_print("="*60)
        safe_print("🎉 E2E TEST SUITE COMPLETED")
        safe_print("="*60)
        
    def run_tests(self, test_labels, **kwargs):
        """테스트 실행 시 아름다운 출력으로 표시"""
        
        safe_print("\n" + "="*60)
        safe_print("🧪 RUNNING TESTS")
        safe_print("="*60)
        
        # 기본 Django 테스트 러너 사용하되 더 나은 출력 제공
        if not test_labels:
            # 라벨이 없으면 전체 테스트 실행
            safe_print("🚀 Running all tests...")
        else:
            safe_print(f"🎯 Running specific tests: {', '.join(test_labels)}")
        
        start_time = time.time()
        
        # 실제 테스트 실행 (부모 클래스의 run_tests 호출)
        failures = super().run_tests(test_labels, **kwargs)
        
        duration = time.time() - start_time
        
        # 결과 요약
        safe_print("\n" + "="*60)
        if failures == 0:
            safe_print("🎉 ALL TESTS PASSED!")
        else:
            safe_print(f"❌ {failures} test(s) failed")
        safe_print(f"⏱️  Total execution time: {duration:.2f} seconds")
        safe_print("="*60)
        
        return failures
        
        return total_failures
    
    def get_resultclass(self):
        """커스텀 결과 클래스 반환"""
        return self._make_result_class()
    
    def _make_result_class(self):
        """커스텀 테스트 결과 클래스 생성"""
        class CustomTextTestResult(unittest.TextTestResult):
            def startTest(self, test):
                super().startTest(test)
                verbosity = getattr(self, 'verbosity', 1)
                if verbosity > 1:
                    test_name = self._format_test_name(test)
                    self.stream.write(f"  🧪 테스트 시작: {test_name}... ")
                    self.stream.flush()

            def addSuccess(self, test):
                super().addSuccess(test)
                verbosity = getattr(self, 'verbosity', 1)
                if verbosity > 1:
                    self.stream.write("✅ 성공\n")

            def addError(self, test, err):
                super().addError(test, err)
                verbosity = getattr(self, 'verbosity', 1)
                if verbosity > 1:
                    self.stream.write("❌ 에러\n")

            def addFailure(self, test, err):
                super().addFailure(test, err)
                verbosity = getattr(self, 'verbosity', 1)
                if verbosity > 1:
                    self.stream.write("❌ 실패\n")

            def addSkip(self, test, reason):
                super().addSkip(test, reason)
                verbosity = getattr(self, 'verbosity', 1)
                if verbosity > 1:
                    self.stream.write("⏭️ 건너뜀\n")

            def _format_test_name(self, test):
                # 한글 docstring이 있으면 그걸 출력, 없으면 기존 방식
                test_method = str(test).split(' ')[0]
                method_name = test_method.split('.')[-1]
                # docstring 우선
                doc = getattr(getattr(test, method_name, None), '__doc__', None)
                if doc:
                    doc = doc.strip().split('\n')[0]
                    return doc
                if method_name.startswith('test_'):
                    method_name = method_name[5:]
                formatted_name = method_name.replace('_', ' ').title()
                return formatted_name
            
        return CustomTextTestResult
