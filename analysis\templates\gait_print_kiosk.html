{% load custom_filters %}

<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>뇌인지 관련 AI 보행 스캔 결과</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 기본 스타일 */
        body {
            font-family: 'Noto Sans KR', sans-serif;
            margin: 0 auto!important;
            background-color: #f5f5f5;
            width: 290mm;
            color: #333;
        }

        h1 {
            font-size: 24px!important;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }        /* 일반 화면용 스타일 */
        @media screen {
            .chart-container {
                width: calc(100% - 10px) !important; /* 화면에서는 약간 줄인 너비 */
                max-width: calc(100% - 10px) !important;
            }
        }        /* 인쇄용 스타일 */
        @media print {
            * {
                -webkit-print-color-adjust: exact !important; /* Chrome, Safari */
                color-adjust: exact !important; /* Firefox */
                print-color-adjust: exact !important; /* 표준 속성 */
            }

            @page { 
                margin: 15mm; /* 페이지 여백을 15mm로 증가 */
            }

            body {
                width: 260mm !important; /* A4 너비에서 여백 고려 (290mm - 30mm) */
                margin: 0 auto !important;
                padding: 0 !important;
            }

            .container {
                max-width: 260mm !important;
                width: 260mm !important;
                margin: 0 auto !important;
                padding: 20mm 15mm !important; /* 상하 20mm, 좌우 15mm 패딩으로 증가 */
                box-sizing: border-box !important;
            }

            .chart-container {
                width: 100% !important; /* 인쇄 시 원래 너비로 복원 */
                max-width: 100% !important;
                box-sizing: border-box !important;
                overflow: hidden !important;
            }

            .chart-row {
                width: 100% !important;
                max-width: 100% !important;
                box-sizing: border-box !important;
            }

            /* 차트 캔버스 크기 제한 */
            canvas {
                max-width: 100% !important;
                height: auto !important;
            }

            /* 인쇄 버튼 숨기기 */
            .print-button-container {
                display: none !important;
            }

            /* 인쇄 시 원형 차트 표시 개선 */
            .score-circle {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }            /* SVG 요소가 인쇄에서 제대로 표시되도록 설정 */
            svg {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* "보행 상태 변화" 섹션을 새 페이지에서 시작 */
            #gait-history-section {
                page-break-before: always !important;
            }
        }


        .container {
            background-color: white;
            padding: 30px;
            max-width: 290mm;
            margin: 0 auto;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 15px;
        }

        .divider {
            height: 2px;
            background-color: #3498db;
            margin: 10px 0 20px 0;
        }

        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin: 30px 0 5px 0;
        }

        .section-subtitle {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 20px;
        }

        /* 점수 원형 차트 */
        .score-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 30px;
        }

        {% comment %} .score-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px auto;
            background: conic-gradient(#FFA500 0% 65%, #e0e0e0 65% 100%);
        }

        .score-inner {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        } {% endcomment %}

        .score-circle {
            width: 150px;
            height: 150px;
            position: relative;
            margin: 20px auto;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .score-circle svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .score-inner {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 2;
        }


        .score-label {
            font-size: 14px;
            color: #666;
        }

        .score-value {
            font-size: 24px;
            font-weight: bold;
        }

        .score-status {
            font-size: 18px;
            font-weight: bold;
            color: #FFA500;
        }

        /* 점수 바 */
        .score-bar-container {
            width: 100%;
            height: 30px;
            position: relative;
            margin: 10px 0;
        }

        .score-bar-danger {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 40%;
            background-color: #FF4136;
        }

        .score-bar-warning {
            position: absolute;
            left: 40%;
            top: 0;
            height: 100%;
            width: 30%;
            background-color: #FFA500;
        }

        .score-bar-normal {
            position: absolute;
            left: 70%;
            top: 0;
            height: 100%;
            width: 30%;
            background-color: #4c9cfa;
        }

        .score-marker {
            position: absolute;
            top: -10px;
            height: 50px;
            width: 2px;
            background-color: #333;
        }

        .score-scale {
            display: flex;
            justify-content: space-between;
            width: 100%;
            margin-bottom: 20px;
        }

        /* 메시지 박스 */
        .message-box {
            border: 1px solid #3498db;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            background-color: #f8f9fa;
        }

        /* 지표 그룹 */
        .metric-group {
            margin-bottom: 10px;
        }

        .metric-group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .metric-group-title {
            font-weight: bold;
            width: 140px;
        }

        .metric-group-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            color: white;
        }

        .metric-group-status.normal {
            background-color: #4c9cfa;
        }

        .metric-group-status.warning {
            background-color: #FFA500;
        }

        .metric-group-status.danger {
            background-color: #FF4136;
        }

        /* 지표 항목 */
        .metric-item {
            display: flex;
            align-items: center;
            margin-bottom: 22px;
        }

        .metric-side {
            width: 30px;
            text-align: center;
            font-weight: bold;
        }

        .metric-bar-container {
            flex-grow: 1;
            height: 20px;
            position: relative;
            margin: 0 10px;
        }

        .metric-bar-segment {
            position: absolute;
            height: 100%;
            top: 0;
        }

        .metric-marker {
            position: absolute;
            top: -5px;
            width: 4px;
            height: 30px;
            background-color: #333;
            z-index: 10;
        }

        .metric-value {
            width: 70px;
            text-align: right;
            font-weight: bold;
        }

        .metric-scale {
            display: flex;
            justify-content: space-between;
            width: calc(100% - 100px);
            margin-left: 30px;
            font-size: 12px;
            color: #7f8c8d;
        }        /* 차트 영역 */
        .chart-container {
            margin: 30px 0;
            height: 250px;
            padding: 10px;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
            overflow: hidden;
        }

        .chart-legend {
            display: flex;
            justify-content: center;
            margin-bottom: 10px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 5px;
        }

        /* 테이블 스타일 */
        .trend-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            text-overflow: ellipsis; 
            white-space: nowrap;
        }
        
        /* 테이블 스크롤 컨테이너 */
        .table-wrapper {
            width: 100%;
            overflow-x: auto;
        }

        .trend-table th, .trend-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            min-width: 80px; /* 최소 너비 설정 */
        }

        .trend-table th {
            background-color: #f2f2f2;
        }

        .trend-table .negative {
            font-weight: bold;
            color: #FF4136;
        }

        /* 모바일에서의 테이블 스타일 */
        @media screen and (max-width: 768px) {
            .table-wrapper {
                margin-bottom: 20px;
            }
        }

        /* 푸터 */
        .footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #7f8c8d;
        }

        /* 버튼 */
        .button-container {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .button {
            padding: 15px 40px;
            border-radius: 30px;
            font-size: 18px;
            font-weight: bold;
            color: white;
            border: none;
            cursor: pointer;
            width: 45%;
            text-align: center;
        }

        .button-exit {
            background-color: #e74c3c;
        }

        .button-retest {
            background-color: #3498db;
        }        /* 구분선 */
        .metric-divider {
            height: 1px;
            background-color: #3498db;
            margin: 10px 0;
            opacity: 0.5;
        }

        /* 컴팩트 레이아웃을 위한 새로운 스타일 */
        .metric-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .metric-column {
            flex: 1;
        }

        .metric-half-group {
            margin-bottom: 15px;
        }

        .metric-half-group .metric-group-header {
            margin-bottom: 8px;
        }

        .metric-half-group .metric-item {
            margin-bottom: 15px;
        }

        .metric-half-group .metric-scale {
            margin-bottom: 10px;
        }/* 차트 레이아웃 */
        .chart-row {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }

        .chart-label {
            width: 100px;
            text-align: left;
            flex-shrink: 0;
        }

        .chart-title {
            font-weight: bold;
            font-size: 16px;
        }

        .chart-subtitle {
            font-size: 12px;
            color: #7f8c8d;
        }

        .chart-container {
            flex: 1;
            height: 200px;
            max-width: calc(100% - 120px);
            box-sizing: border-box;
            overflow: hidden;
        }        /* 인쇄 시 스타일 */
        @media print {
            .chart-container {
                width: 100% !important;
                max-width: 100% !important;
            }
        }

        @media screen {
            .chart-container {
                width: calc(100% - 10px) !important;
                max-width: calc(100% - 10px) !important;
            }
        }


    </style>
</head>
<body>
<div class="container">
    <h1>뇌인지 관련 AI 보행 스캔 결과</h1>
    <div class="divider"></div>
    <div class="print-button-container" style="position: fixed; top: 20px; right: 20px; z-index: 1000;">
        <button id="print-report" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-full shadow-lg transition duration-200 flex items-center">
            <i class="bi bi-printer mr-2"></i> 인쇄하기
        </button>
    </div>

    <script>
        // 모바일 환경 감지 함수
        function isMobileDevice() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                   (navigator.userAgent.includes('Mobile') && navigator.userAgent.includes('WebView'));
        }

        if (isMobileDevice()) {
            const printContainer = document.querySelector(".print-button-container");
            printContainer.style.display = "none";
        }




        // PDF 생성 및 다운로드 함수
        function generatePDF() {
            // 인쇄 버튼 숨기기
            const printContainer = document.querySelector(".print-button-container");
            printContainer.style.display = "none";

            // 모바일에서는 PDF 생성, 데스크톱에서는 일반 인쇄
            if (isMobileDevice()) {
                // 컨테이너 요소 가져오기
                const container = document.querySelector('.container');

                // 점수 원 상태 가져오기
                const scoreCircle = document.getElementById('scoreCircle');
                const score = parseFloat(scoreCircle.dataset.score || '65');
                const color = scoreCircle.dataset.color || '#FFA500';

                // 점수 원 상태를 저장해둡니다
                const scoreState = {
                    score: score,
                    color: color
                };                // PDF 생성 옵션
                const options = {
                    scale: 2, // 고해상도를 위한 스케일 조정
                    useCORS: true, // 외부 이미지 허용
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: false, // 로깅 끄기
                    imageTimeout: 0, // 이미지 로딩 시간제한 없음
                    removeContainer: true, // 임시 컨테이너 제거
                    width: container.scrollWidth, // 컨테이너의 실제 너비 사용
                    height: container.scrollHeight, // 컨테이너의 실제 높이 사용
                    onclone: function(clonedDoc) {
                        try {
                            // 복사된 문서에서 차트 컨테이너 크기 제한
                            const chartContainers = clonedDoc.querySelectorAll('.chart-container');
                            chartContainers.forEach(container => {
                                container.style.width = '100%';
                                container.style.maxWidth = '100%';
                                container.style.overflow = 'hidden';
                                container.style.boxSizing = 'border-box';
                            });

                            // 차트 행 크기 제한
                            const chartRows = clonedDoc.querySelectorAll('.chart-row');
                            chartRows.forEach(row => {
                                row.style.width = '100%';
                                row.style.maxWidth = '100%';
                                row.style.boxSizing = 'border-box';
                            });

                            // 캔버스 크기 제한
                            const canvases = clonedDoc.querySelectorAll('canvas');
                            canvases.forEach(canvas => {
                                canvas.style.maxWidth = '100%';
                                canvas.style.height = 'auto';
                            });

                            // 복사된 문서에서 SVG 요소 처리
                            const scoreIndicator = clonedDoc.getElementById('scoreIndicator');
                            const scoreInner = clonedDoc.querySelector('.score-inner');

                            if (scoreIndicator) {
                                // 원의 둘레 계산 (2πr)
                                const circumference = 2 * Math.PI * 65;

                                // 점수에 따른 채워진 부분 계산
                                const fillPercentage = scoreState.score / 100;
                                const dashArray = `${fillPercentage * circumference} ${circumference}`;

                                // 색상 및 대시 어레이 설정 (모든 가능한 방법 사용)
                                scoreIndicator.setAttribute('stroke', scoreState.color);
                                scoreIndicator.setAttribute('stroke-dasharray', dashArray);
                                scoreIndicator.style.stroke = scoreState.color;
                                scoreIndicator.style.strokeDasharray = dashArray;
                            }

                            // 점수 원 레이아웃 고정
                            if (scoreInner) {
                                scoreInner.style.position = 'absolute';
                                scoreInner.style.top = '50%';
                                scoreInner.style.left = '50%';
                                scoreInner.style.transform = 'translate(-50%, -50%)';
                                scoreInner.style.zIndex = '10';
                            }
                        } catch (e) {
                            console.error('복제 문서 처리 오류:', e);
                        }
                    }
                };

                // SVG 요소를 정확하게 처리하기 위한 지연 추가
                setTimeout(() => {
                    // html2canvas로 HTML을 이미지로 변환
                    html2canvas(container, options).then(function(canvas) {
                    // 인쇄 버튼 다시 표시
                    printContainer.style.display = "block";

                    // jsPDF 인스턴스 생성 (A4 사이즈)
                    const { jsPDF } = window.jspdf;
                    const pdf = new jsPDF('p', 'mm', 'a4');

                    // A4 사이즈 (210mm x 297mm)
                    const pdfWidth = 210;
                    const pdfHeight = 297;

                    // 캔버스 이미지를 데이터 URL로 변환
                    const imgData = canvas.toDataURL('image/jpeg', 1.0);

                    // 이미지 비율 계산
                    const imgWidth = pdfWidth;
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;

                    // 여러 페이지로 나누기
                    let heightLeft = imgHeight;
                    let position = 0;
                    let pageNumber = 1;

                    // 첫 페이지 추가
                    pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pdfHeight;

                    // 필요한 경우 추가 페이지 생성
                    while (heightLeft > 0) {
                        position = -pdfHeight * pageNumber;
                        pdf.addPage();
                        pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
                        heightLeft -= pdfHeight;
                        pageNumber++;
                    }

                    // PDF 파일 생성 및 새 창에서 열기
                    const pdfBlob = pdf.output('blob');
                    const pdfUrl = URL.createObjectURL(pdfBlob);
                    window.open(pdfUrl, '_blank');
                    }).catch(error => {
                        console.error('PDF 생성 오류:', error);
                        printContainer.style.display = "block";
                    });
                }, 100); // 100ms 지연으로 SVG 렌더링 완료 확인
            } else {
                // 데스크톱에서는 기본 인쇄 기능 사용
                window.print();

                // 인쇄 후 버튼 다시 표시
                setTimeout(() => {
                    printContainer.style.display = "block";
                }, 1000);
            }
        }


        document.getElementById('print-report').addEventListener('click', function() {
            // 인쇄 버튼 클릭 시 새로운 함수 호출
            generatePDF();
        });

    </script>

    <!-- 점수 원형 차트 -->
    <div class="score-container">
        <div id="scoreCircle" class="score-circle">
            <svg width="150" height="150" viewBox="0 0 150 150" id="scoreSvg" preserveAspectRatio="xMidYMid meet">
                <defs>
                    <clipPath id="circleClip">
                        <circle cx="75" cy="75" r="75"/>
                    </clipPath>
                </defs>
                <g clip-path="url(#circleClip)">
                    <!-- 배경 원 -->
                    <circle cx="75" cy="75" r="65" fill="none" stroke="#e0e0e0" stroke-width="15"/>
                    <!-- 점수 원 -->
                    <circle cx="75" cy="75" r="65" fill="none" stroke="#FFA500" stroke-width="15"
                        stroke-dasharray="0 408" id="scoreIndicator" transform="rotate(-90 75 75)"/>
                </g>
            </svg>
            <div class="score-inner">
                <div class="score-label">보행점수</div>
                <div id="scoreValue" class="score-value">65.0점</div>
                <div id="scoreStatus" class="score-status">주의</div>
            </div>
        </div>

        <!-- 점수 바 -->
        <div class="score-bar-container">
            <div class="score-bar-danger"></div>
            <div class="score-bar-warning"></div>
            <div class="score-bar-normal"></div>
            <div id="scoreMarker" class="score-marker" style="left: 65%;"></div>
        </div>
        <div class="score-scale">
            <div>0.0</div>
            <div>40.0</div>
            <div>70.0</div>
            <div>100.0</div>
        </div>
    </div>

    <!-- 메시지 박스 -->
    <div class="message-box">
        <p>뇌인지 기능과 관련된 현재 보행 상태는 <span id="statusText">주의</span>군에 속합니다.</p>
        <p>정확한 진단을 위해 신경과 전문의와의 상담이 필요합니다.</p>
    </div>

    <!-- 날짜 선택 드롭다운 -->
    <div class="date" style="display: flex; align-items: center; gap: 12px;" class="section-title">
        <label for="date-select" style="font-weight: bold; font-size: 1.08em; letter-spacing: 0.5px;">검사 날짜</label>
        <form method="get" id="date-select-form" style="display:inline; margin: 0;">
            <select name="date" id="date-select" style="padding:1px 2px; border-radius:6px; border:1px solid #ccc; min-width: 120px;">
                {% for date in date_list %}
                    <option value="{{ date }}" {% if date == selected_date %}selected{% endif %}>{{ date }}</option>
                {% endfor %}
            </select>
        </form>
    </div>

    <script>
        if (isMobileDevice()) {
        // Django 템플릿에서 세션 유무를 JS 변수로 전달
        const isAuthenticated = {{ user.is_authenticated|yesno:"true,false" }};
        const jwtHeader = `{{ header|default_if_none:"" }}`;

            if (window.location.href.includes('date=')) {
                document.getElementById('date-select-form').addEventListener('change', function() {
                    document.getElementById('date-select-form').submit();
                    return;
                });
            }
            else {
                // 세션이 없을 때만 JWT로 세션 발급
                document.getElementById('date-select').addEventListener('change', function(e) {
                    const select = document.getElementById('date-select');
                    const selectedDate = select.value;
                    const params = new URLSearchParams(window.location.search);
                    params.set('date', selectedDate);

                    fetch(`${window.location.origin}/api/jwt-to-session/`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${jwtHeader}`
                        },
                        credentials: 'same-origin',
                    })
                    .then(response => {
                        if (response.status === 200) {
                                document.getElementById('date-select-form').submit();
                                return;
                        }
                            return response.text().then(text => { throw new Error(text); });
                        })
                    .catch(error => {
                        alert('로그인 인증 정보를 불러올 수 없습니다.\n' + error);
                    });
                });
                            }
                            
                        } else {
                            document.getElementById('date-select-form').addEventListener('change', function() {
                                document.getElementById('date-select-form').submit();
                            });
                        }
    </script>



    <div class="section-subtitle">(Gait Scan Date)</div>

    <!-- 보행 상세 상태 -->
    <div class="section-title">보행 상세 상태</div>
    <div class="section-subtitle">(Gait Detail Condition)</div>

    <div id="metricsContainer">
    <!-- JavaScript로 동적 생성 -->
    </div>    <!-- 보행 상태 변화 -->
    <div class="metric-divider"></div>
    {% comment %} <div style="text-align: center;"><i class="bi bi-arrow-down-short" style="font-size: 24px;"></i></div> {% endcomment %}
    <div class="section-divider"></div>
    <!-- 보행 상태 변화 -->
    <div id="gait-history-section" class="section-title">보행 상태 변화</div>
    <div class="section-subtitle">(History of Gait Condition)</div>


    <!-- 범례 -->
    <div class="chart-legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: #4c9cfa;"></div>
            <span>양호</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #FFA500;"></div>
            <span>주의</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #FF4136;"></div>
            <span>위험</span>
        </div>
    </div>

    <!-- 차트 영역 -->
    <div class="chart-row">
        <div class="chart-label">
            <div class="chart-title">보행점수</div>
            <div class="chart-subtitle">(Gait Score)</div>
        </div>
        <div class="chart-container">
            <canvas id="gaitScoreChart"></canvas>
        </div>
    </div>

    <div class="chart-row">
        <div class="chart-label">
            <div class="chart-title">보행속도</div>
            <div class="chart-subtitle">(Gait Speed)</div>
        </div>
        <div class="chart-container">
            <canvas id="gaitSpeedChart"></canvas>
        </div>
    </div>


    <!-- 스캔 일시 테이블 -->
    <div class="table-wrapper">
        <table class="trend-table" id="trendTable">
            <tr>
                <th>스캔일시</th>
            </tr>
            <tr>
                <th>변화율(20%)</th>
            </tr>
        </table>
    </div>

    <!-- 주의 문구 -->
    <div class="footer">
        * 위 내용은 스캔 결과는 의학적 판단 및 진단과는 무관합니다.<br>
        * 건강관리 목적이 아닌 의료적인 진단을 위해서는 의사와 상담하시기 바랍니다.
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script>
    // 데이터 가져오기 (서버에서 전달받은 데이터)
    const gaitData = {{ gait_data|safe }};
    const gaitTrendData = {{ gait_trend_data|safe }} || {dates: [], velocity: [], cadence: [], score: []};
    const normalRanges = {{ normal_ranges|safe }};
    const codeInfo = {{ code_info|safe }};

    console.log(gaitData);
    console.log(normalRanges);
    console.log(codeInfo);
    console.log(gaitTrendData);

    // 페이지 로드 시 초기화
    document.addEventListener('DOMContentLoaded', function () {
        // 원래 toFixed 메서드 저장
        const originalToFixed = Number.prototype.toFixed;

// toFixed 메서드 오버라이딩
        Number.prototype.toFixed = function (digits) {
            // 버림 처리를 위한 로직
            const factor = Math.pow(10, digits);
            const truncated = Math.floor(this * factor) / factor;

            // 원래 toFixed 메서드를 호출하여 문자열 포맷팅
            return originalToFixed.call(truncated, digits);
        };


        setupScoreCircle();
        createMetricBars();
        createCharts();
        createTrendTable();
    });

    // 점수 원형 차트 설정
    function setupScoreCircle() {
        const score = gaitData.score * 10;
        const scoreValue = document.getElementById('scoreValue');
        const scoreStatus = document.getElementById('scoreStatus');
        const scoreMarker = document.getElementById('scoreMarker');
        const statusText = document.getElementById('statusText');

        // 점수 표시
        scoreValue.textContent = score.toFixed(1) + ' 점';

        // 상태 결정
        let status, color;
        if (score >= normalRanges.score.normal_min * 10) {
            status = '정상';
            color = '#4c9cfa';
        } else if (score >= 40.0) {
            status = '주의';
            color = '#FFA500';
        } else {
            status = '위험';
            color = '#FF4136';
        }

        scoreStatus.textContent = status;
        statusText.textContent = status;
        scoreStatus.style.color = color;

        // SVG 원형 차트 설정
        const scoreIndicator = document.getElementById('scoreIndicator');
        if (scoreIndicator) {
            // 원의 둘레 계산 (2πr)
            const circumference = 2 * Math.PI * 65;

            // 점수에 따른 채워진 부분 계산
            const fillPercentage = score / 100;
            const dashArray = `${fillPercentage * circumference} ${circumference}`;

            // 색상 및 대시 어레이 설정
            scoreIndicator.setAttribute('stroke', color);
            scoreIndicator.setAttribute('stroke-dasharray', dashArray);

            // 속성 직접 설정 (호환성 개선)
            scoreIndicator.style.stroke = color;
            scoreIndicator.style.strokeDasharray = dashArray;
        }

        // 폴백: SVG가 제대로 렌더링되지 않는 경우를 대비
        const scoreCircle = document.getElementById('scoreCircle');
        if (scoreCircle) {
            // 데이터 속성에 점수와 색상 저장 (후처리용)
            scoreCircle.dataset.score = score;
            scoreCircle.dataset.color = color;

            // 모바일 환경에서 레이아웃 문제 방지
            if (isMobileDevice()) {
                // 점수 원 내부 요소 위치 고정
                const scoreInner = document.querySelector('.score-inner');
                if (scoreInner) {
                    scoreInner.style.position = 'absolute';
                    scoreInner.style.top = '50%';
                    scoreInner.style.left = '50%';
                    scoreInner.style.transform = 'translate(-50%, -50%)';
                    scoreInner.style.zIndex = '10';
                }
            }
        }

        // 점수 마커 위치 설정
        scoreMarker.style.left = `${score}%`;
    }



    // 지표 상태 결정 함수
    function getMetricStatus(value, min, max, normalMin, normalMax, cautionMin, cautionMax, direction) {
        if (direction === 'positive') {
            // 높을수록 좋은 지표
            if (value >= normalMin && value <= normalMax) {
                return {status: '양호', class: 'normal'};
            } else if ((value >= cautionMin && value < normalMin) || (value > normalMax && value <= cautionMax)) {
                return {status: '주의', class: 'warning'};
            } else {
                return {status: '위험', class: 'danger'};
            }
        } else {
            // 낮을수록 좋은 지표 (negative)
            if (value >= normalMin && value <= normalMax) {
                return {status: '양호', class: 'normal'};
            } else if ((value >= cautionMin && value < normalMin) || (value > normalMax && value <= cautionMax)) {
                return {status: '주의', class: 'warning'};
            } else {
                return {status: '위험', class: 'danger'};
            }
        }
    }    // 지표 바 생성 함수
    function createMetricBars() {
        const container = document.getElementById('metricsContainer');
        container.innerHTML = ''; // 컨테이너 초기화

        // 보행속도 (단일 항목)
        createSingleMetric(container, '보행속도', gaitData.velocity, codeInfo.velocity.unit_name, codeInfo.velocity);

        // 구분선 추가
        container.innerHTML += '<div class="metric-divider"></div>';

        // 첫 번째 행: 보폭, 스윙시간비율
        container.innerHTML += '<div class="metric-row">';
        const firstRowContainer = document.createElement('div');
        firstRowContainer.className = 'metric-row';
        
        // 보폭 (좌/우) - 왼쪽 컬럼
        const strideColumn = document.createElement('div');
        strideColumn.className = 'metric-column';
        createDualMetricCompact(strideColumn, '보폭', gaitData.stride_len_l, gaitData.stride_len_r, codeInfo.stride_len_l.unit_name, codeInfo.stride_len_l);
        firstRowContainer.appendChild(strideColumn);

        // 스윙시간비율 (좌/우) - 오른쪽 컬럼
        const swingColumn = document.createElement('div');
        swingColumn.className = 'metric-column';
        createDualMetricCompact(swingColumn, '스윙시간비율', gaitData.swing_perc_l, gaitData.swing_perc_r, codeInfo.swing_perc_l.unit_name, codeInfo.swing_perc_l);
        firstRowContainer.appendChild(swingColumn);

        container.appendChild(firstRowContainer);

        // 구분선 추가
        container.innerHTML += '<div class="metric-divider"></div>';

        // 두 번째 행: 접지시간비율, 양발접지시간비율
        const secondRowContainer = document.createElement('div');
        secondRowContainer.className = 'metric-row';
        
        // 접지시간비율 (좌/우) - 왼쪽 컬럼
        const stanceColumn = document.createElement('div');
        stanceColumn.className = 'metric-column';
        createDualMetricCompact(stanceColumn, '접지시간비율', gaitData.stance_perc_l, gaitData.stance_perc_r, codeInfo.stance_perc_l.unit_name, codeInfo.stance_perc_l);
        secondRowContainer.appendChild(stanceColumn);

        // 양발접지시간비율 (좌/우) - 오른쪽 컬럼
        const doubleSuppColumn = document.createElement('div');
        doubleSuppColumn.className = 'metric-column';
        createDualMetricCompact(doubleSuppColumn, '양발접지시간비율', gaitData.d_supp_perc_l, gaitData.d_supp_perc_r, codeInfo.d_supp_perc_l.unit_name, codeInfo.d_supp_perc_l);
        secondRowContainer.appendChild(doubleSuppColumn);

        container.appendChild(secondRowContainer);
    }

    // 단일 지표 생성 함수 (보행속도)
    function createSingleMetric(container, title, value, unit, codeData) { // 보행속도만 사용함
        // 값이 범위를 벗어나지 않도록 조정
        value = Math.max(codeData.min_value, Math.min(codeData.max_value, value));
        const statusInfo = getMetricStatus(
            value,
            codeData.min_value,
            codeData.max_value,
            codeData.normal_min_value,
            codeData.normal_max_value,
            codeData.caution_min_value,
            codeData.caution_max_value,
            codeData.direction
        );

        // HTML 생성
        const html = `
        <div class="metric-group">
            <div class="metric-group-header">
                <div class="metric-group-title">${title}</div>
                <div class="metric-group-status ${statusInfo.class}">${statusInfo.status}</div>
            </div>

            <div class="metric-item">
                <div class="metric-side"></div>
                <div class="metric-bar-container">
                    ${createBarSegments(
            codeData.min_value,
            codeData.max_value,
            codeData.normal_min_value,
            codeData.normal_max_value,
            codeData.caution_min_value,
            codeData.caution_max_value,
            codeData.direction
        )}
                    <div class="metric-marker" style="left: ${getPercentage(value, codeData.min_value, codeData.max_value)}%;"></div>
                </div>
                <div class="metric-value" style="color: ${getValueColor(statusInfo.class)}">${value.toFixed(1)}${unit}</div>
            </div>

            <div class="metric-scale">
                <div>${codeData.min_value.toFixed(1)}</div>
                <div>${codeData.max_value.toFixed(1)}</div>
            </div>
        </div>
    `;

        container.innerHTML += html;
    }    // 이중 지표 생성 함수 (좌/우)
    function createDualMetric(container, title, leftValue, rightValue, unit, codeData) {
        // 값이 범위를 벗어나지 않도록 조정
        leftValue = Math.max(codeData.min_value, Math.min(codeData.max_value, leftValue));
        rightValue = Math.max(codeData.min_value, Math.min(codeData.max_value, rightValue));

        const leftStatus = getMetricStatus(
            leftValue,
            codeData.min_value,
            codeData.max_value,
            codeData.normal_min_value,
            codeData.normal_max_value,
            codeData.caution_min_value,
            codeData.caution_max_value,
            codeData.direction
        );

        const rightStatus = getMetricStatus(
            rightValue,
            codeData.min_value,
            codeData.max_value,
            codeData.normal_min_value,
            codeData.normal_max_value,
            codeData.caution_min_value,
            codeData.caution_max_value,
            codeData.direction
        );

        // 전체 상태 결정 (좌/우 중 더 심각한 상태)
        let overallStatus, overallClass;
        if (leftStatus.class === 'danger' || rightStatus.class === 'danger') {
            overallStatus = '위험';
            overallClass = 'danger';
        } else if (leftStatus.class === 'warning' || rightStatus.class === 'warning') {
            overallStatus = '주의';
            overallClass = 'warning';
        } else {
            overallStatus = '양호';
            overallClass = 'normal';
        }

        // HTML 생성
        const html = `
        <div class="metric-group">
            <div class="metric-group-header">
                <div class="metric-group-title">${title}</div>
                <div class="metric-group-status ${overallClass}">${overallStatus}</div>
            </div>

            <div class="metric-item">
                <div class="metric-side">좌</div>
                <div class="metric-bar-container">
                    ${createBarSegments(
            codeData.min_value,
            codeData.max_value,
            codeData.normal_min_value,
            codeData.normal_max_value,
            codeData.caution_min_value,
            codeData.caution_max_value,
            codeData.direction
        )}
                    <div class="metric-marker" style="left: ${getPercentage(leftValue, codeData.min_value, codeData.max_value)}%;"></div>
                </div>
                <div class="metric-value" style="color: ${getValueColor(leftStatus.class)}">${leftValue.toFixed(1)}${unit}</div>
            </div>

            <div class="metric-item">
                <div class="metric-side">우</div>
                <div class="metric-bar-container">
                    ${createBarSegments(
            codeData.min_value,
            codeData.max_value,
            codeData.normal_min_value,
            codeData.normal_max_value,
            codeData.caution_min_value,
            codeData.caution_max_value,
            codeData.direction
        )}
                    <div class="metric-marker" style="left: ${getPercentage(rightValue, codeData.min_value, codeData.max_value)}%;"></div>
                </div>
                <div class="metric-value" style="color: ${getValueColor(rightStatus.class)}">${rightValue.toFixed(1)}${unit}</div>
            </div>

            <div class="metric-scale">
                <div>${codeData.min_value.toFixed(1)}</div>
                <div>${codeData.max_value.toFixed(1)}</div>
            </div>
        </div>
    `;

        container.innerHTML += html;
    }

    // 컴팩트한 이중 지표 생성 함수 (좌/우 - 2컬럼 레이아웃용)
    function createDualMetricCompact(container, title, leftValue, rightValue, unit, codeData) {
        // 값이 범위를 벗어나지 않도록 조정
        leftValue = Math.max(codeData.min_value, Math.min(codeData.max_value, leftValue));
        rightValue = Math.max(codeData.min_value, Math.min(codeData.max_value, rightValue));

        const leftStatus = getMetricStatus(
            leftValue,
            codeData.min_value,
            codeData.max_value,
            codeData.normal_min_value,
            codeData.normal_max_value,
            codeData.caution_min_value,
            codeData.caution_max_value,
            codeData.direction
        );

        const rightStatus = getMetricStatus(
            rightValue,
            codeData.min_value,
            codeData.max_value,
            codeData.normal_min_value,
            codeData.normal_max_value,
            codeData.caution_min_value,
            codeData.caution_max_value,
            codeData.direction
        );

        // 전체 상태 결정 (좌/우 중 더 심각한 상태)
        let overallStatus, overallClass;
        if (leftStatus.class === 'danger' || rightStatus.class === 'danger') {
            overallStatus = '위험';
            overallClass = 'danger';
        } else if (leftStatus.class === 'warning' || rightStatus.class === 'warning') {
            overallStatus = '주의';
            overallClass = 'warning';
        } else {
            overallStatus = '양호';
            overallClass = 'normal';
        }

        // HTML 생성
        const html = `
        <div class="metric-half-group">
            <div class="metric-group-header">
                <div class="metric-group-title">${title}</div>
                <div class="metric-group-status ${overallClass}">${overallStatus}</div>
            </div>

            <div class="metric-item">
                <div class="metric-side">좌</div>
                <div class="metric-bar-container">
                    ${createBarSegments(
            codeData.min_value,
            codeData.max_value,
            codeData.normal_min_value,
            codeData.normal_max_value,
            codeData.caution_min_value,
            codeData.caution_max_value,
            codeData.direction
        )}
                    <div class="metric-marker" style="left: ${getPercentage(leftValue, codeData.min_value, codeData.max_value)}%;"></div>
                </div>
                <div class="metric-value" style="color: ${getValueColor(leftStatus.class)}">${leftValue.toFixed(1)}${unit}</div>
            </div>

            <div class="metric-item">
                <div class="metric-side">우</div>
                <div class="metric-bar-container">
                    ${createBarSegments(
            codeData.min_value,
            codeData.max_value,
            codeData.normal_min_value,
            codeData.normal_max_value,
            codeData.caution_min_value,
            codeData.caution_max_value,
            codeData.direction
        )}
                    <div class="metric-marker" style="left: ${getPercentage(rightValue, codeData.min_value, codeData.max_value)}%;"></div>
                </div>
                <div class="metric-value" style="color: ${getValueColor(rightStatus.class)}">${rightValue.toFixed(1)}${unit}</div>
            </div>

            <div class="metric-scale">
                <div>${codeData.min_value.toFixed(1)}</div>
                <div>${codeData.max_value.toFixed(1)}</div>
            </div>
        </div>
    `;

        container.innerHTML += html;
    }

    // 값 색상 결정 함수
    function getValueColor(statusClass) {
        switch (statusClass) {
            case 'normal':
                return '#000000'; // 정상은 검은색
            case 'warning':
                return '#FFA500'; // 주의는 노란색
            case 'danger':
                return '#FF4136';  // 위험은 빨간색
            default:
                return '#000000';
        }
    }

    // 바 세그먼트 HTML 생성 함수
    function createBarSegments(min, max, normalMin, normalMax, cautionMin, cautionMax, direction) {
        const range = max - min;

        if (direction === 'positive') {
            // 높을수록 좋은 지표
            const normalWidth = ((normalMax - normalMin) / range) * 100;
            const normalLeft = ((normalMin - min) / range) * 100;

            // 위험 구간 (왼쪽)
            const dangerWidth1 = ((cautionMin - min) / range) * 100;
            // 주의 구간 (왼쪽 정상 사이)
            const warningWidth1 = ((normalMin - cautionMin) / range) * 100;
            // 주의 구간 (오른쪽 정상 이후)
            const warningWidth2 = ((cautionMax - normalMax) / range) * 100;
            // 위험 구간 (오른쪽)
            const dangerWidth2 = ((max - cautionMax) / range) * 100;

            return `
            <div class="metric-bar-segment" style="left: 0; width: ${dangerWidth1}%; background-color: #FF4136;"></div>
            <div class="metric-bar-segment" style="left: ${dangerWidth1}%; width: ${warningWidth1}%; background-color: #FFA500;"></div>
            <div class="metric-bar-segment" style="left: ${normalLeft}%; width: ${normalWidth}%; background-color: #4c9cfa;"></div>
            <div class="metric-bar-segment" style="left: ${normalLeft + normalWidth}%; width: ${warningWidth2}%; background-color: #FFA500;"></div>
            <div class="metric-bar-segment" style="left: ${normalLeft + normalWidth + warningWidth2}%; width: ${dangerWidth2}%; background-color: #FF4136;"></div>
        `;
        } else {
            // 낮을수록 좋은 지표 (negative)
            const normalWidth = ((normalMax - normalMin) / range) * 100;
            const normalLeft = ((normalMin - min) / range) * 100;

            // 위험 구간 (왼쪽)
            const dangerWidth1 = ((cautionMin - min) / range) * 100;
            // 주의 구간 (왼쪽 정상 사이)
            const warningWidth1 = ((normalMin - cautionMin) / range) * 100;
            // 주의 구간 (오른쪽 정상 이후)
            const warningWidth2 = ((cautionMax - normalMax) / range) * 100;
            // 위험 구간 (오른쪽)
            const dangerWidth2 = ((max - cautionMax) / range) * 100;

            return `
            <div class="metric-bar-segment" style="left: 0; width: ${dangerWidth1}%; background-color: #FF4136;"></div>
            <div class="metric-bar-segment" style="left: ${dangerWidth1}%; width: ${warningWidth1}%; background-color: #FFA500;"></div>
            <div class="metric-bar-segment" style="left: ${normalLeft}%; width: ${normalWidth}%; background-color: #4c9cfa;"></div>
            <div class="metric-bar-segment" style="left: ${normalLeft + normalWidth}%; width: ${warningWidth2}%; background-color: #FFA500;"></div>
            <div class="metric-bar-segment" style="left: ${normalLeft + normalWidth + warningWidth2}%; width: ${dangerWidth2}%; background-color: #FF4136;"></div>
        `;
        }
    }

    // 퍼센트 계산 함수 - 범위를 벗어나지 않도록 제한
    function getPercentage(value, min, max) {
        // 값이 최소값보다 작으면 0%로 제한
        if (value < min) return 0;
        // 값이 최대값보다 크면 100%로 제한
        if (value > max) return 100;
        // 정상 범위 내에 있으면 비율 계산
        return ((value - min) / (max - min)) * 100;
    }

    // 차트 생성
// 차트 생성
function createCharts() {
    // --- 공통 설정 ---
    const backgroundColors = {
        normal: 'rgba(76, 156, 250, 0.2)',  // 파란색 (정상)
        caution: 'rgba(255, 165, 0, 0.2)',  // 노란색 (주의)
        danger: 'rgba(255, 65, 54, 0.2)'    // 빨간색 (위험)
    };

    // 하드코딩된 임계값
    const scoreThresholds = {
        max: 100,
        normal_min: 70,
        caution_min: 40,
        min: 0
    };

    const speedThresholds = {
        max: 120,
        normal_min: 71,
        caution_min: 50,
        min: 25
    };

    // --- 배경 그리기 플러그인 생성 함수 ---
   function createBackgroundPlugin(pluginId, thresholds, colors) {
    return {
        id: pluginId,
        beforeDatasetsDraw: (chart, args, options) => {
            const { ctx, chartArea, scales: { y: yAxis } } = chart;

            // 여기서 numericThresholds 변수를 정의
            const numericThresholds = {
                max: Number(thresholds.max),
                normal_min: Number(thresholds.normal_min),
                caution_min: Number(thresholds.caution_min),
                min: Number(thresholds.min)
            };

            if (!chartArea || !yAxis) {
                return;
            }

            const chartWidth = chartArea.right - chartArea.left;

            // Y축 값에 해당하는 픽셀 위치 계산
            const maxPixel = yAxis.getPixelForValue(numericThresholds.max);
            const normalMinPixel = yAxis.getPixelForValue(numericThresholds.normal_min);
            const cautionMinPixel = yAxis.getPixelForValue(numericThresholds.caution_min);
            const minPixel = yAxis.getPixelForValue(numericThresholds.min);

            ctx.save();
            ctx.beginPath();
            ctx.rect(chartArea.left, chartArea.top, chartWidth, chartArea.bottom - chartArea.top);
            ctx.clip();

            // 1. 정상 영역 (파란색)
            ctx.fillStyle = colors.normal;
            ctx.fillRect(chartArea.left, maxPixel, chartWidth, normalMinPixel - maxPixel);

            // 2. 주의 영역 (노란색)
            ctx.fillStyle = colors.caution;
            ctx.fillRect(chartArea.left, normalMinPixel, chartWidth, cautionMinPixel - normalMinPixel);

            // 3. 위험 영역 (빨간색)
            ctx.fillStyle = colors.danger;
            ctx.fillRect(chartArea.left, cautionMinPixel, chartWidth, minPixel - cautionMinPixel);

            ctx.restore();
        }
    };
}


    // --- 보행 점수 차트 ---
    const scoreCtx = document.getElementById('gaitScoreChart').getContext('2d');
    const scoreBackgroundPlugin = createBackgroundPlugin('scoreBackground', scoreThresholds, backgroundColors);

    // Chart.js에 플러그인 등록 (중요: 전역 등록)
    Chart.register(scoreBackgroundPlugin);

    const scoreChartConfig = {
        type: 'line',
        data: {
            labels: gaitTrendData.dates || [],
            datasets: [{
                label: '보행 점수',
                data: (gaitTrendData.score || []).map(score => parseFloat((score * 10).toFixed(1))),
                borderColor: '#3498db',
                backgroundColor: 'transparent',
                borderWidth: 2,
                pointBackgroundColor: '#3498db',
                pointRadius: 4,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    min: scoreThresholds.min,
                    max: scoreThresholds.max,
                    ticks: { stepSize: 10 }
                },
                x: { grid: { display: false } }
            },
            plugins: {
                legend: { display: false }
            }
        }
    };

    if (window.scoreChartInstance) {
        window.scoreChartInstance.destroy();
    }
    window.scoreChartInstance = new Chart(scoreCtx, scoreChartConfig);

    // --- 보행 속도 차트 ---
    const speedCtx = document.getElementById('gaitSpeedChart').getContext('2d');
    const speedBackgroundPlugin = createBackgroundPlugin('speedBackground', speedThresholds, backgroundColors);

    // Chart.js에 플러그인 등록 (중요: 전역 등록)
    Chart.register(speedBackgroundPlugin);

    const speedChartConfig = {
        type: 'line',
        data: {
            labels: gaitTrendData.dates || [],
            datasets: [{
                label: '보행 속도',
                data: (gaitTrendData.velocity || []).map(v => parseFloat(v.toFixed(1))),
                borderColor: '#2ecc71',
                backgroundColor: 'transparent',
                borderWidth: 2,
                pointBackgroundColor: '#2ecc71',
                pointRadius: 4,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    min: speedThresholds.min,
                    max: speedThresholds.max
                },
                x: { grid: { display: false } }
            },
            plugins: {
                legend: { display: false }
            }
        }
    };

    if (window.speedChartInstance) {
        window.speedChartInstance.destroy();
    }
    window.speedChartInstance = new Chart(speedCtx, speedChartConfig);
}



    // 트렌드 테이블 생성
    function createTrendTable() {
        const table = document.getElementById('trendTable');
        const dateRow = table.rows[0];
        const changeRow = table.rows[1];

        // 기존 셀 제거 (헤더 제외)
        while (dateRow.cells.length > 1) {
            dateRow.deleteCell(1);
        }
        while (changeRow.cells.length > 1) {
            changeRow.deleteCell(1);
        }
        
        if (gaitTrendData.dates.length > 4) {
            table.style.overflowX = 'scroll'; // 가로 스크롤 가능
        }
        else {
            table.style.overflowX = 'hidden'; // 가로 스크롤 불가능
        }

        // 새 셀 추가
        gaitTrendData.dates.forEach((date, index) => {
            const dateCell = dateRow.insertCell(-1);
            dateCell.textContent = date;

            const changeCell = changeRow.insertCell(-1);
            if (index === 0) {
                changeCell.textContent = '-';
            } else {
                const prevScore = gaitTrendData.score[index - 1];
                const currentScore = gaitTrendData.score[index];
                const changePercent = ((currentScore - prevScore) / prevScore * 100).toFixed(1);

                if (parseFloat(changePercent) < -20) {
                    changeCell.textContent = '악화';
                    changeCell.className = 'negative';
                } else {
                    changeCell.textContent = '양호';
                }
            }
        });


    }

</script>
</body>
</html>
