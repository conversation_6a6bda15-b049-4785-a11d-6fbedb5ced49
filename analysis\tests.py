from django.test import SimpleTestCase, TestCase, TransactionTestCase
from django.urls import reverse, resolve
from django.contrib.auth import views as auth_views
from django.db import transaction
from django.core.management import call_command
from .custom.custom_token import CustomTokenObtainPairView, CustomTokenRefreshView

from rest_framework.test import APIClient, APITransactionTestCase
from rest_framework import status
from django.contrib.auth.hashers import make_password
from .models import (
    AuthInfo, UserInfo, BodyResult, GaitResult, 
    OrganizationInfo, SessionInfo, KioskInfo, FamilyUserInfo
)
from . import views, views_mobile, views_kiosk, views_aos

import json
import time
from datetime import datetime, timedelta

base_url = 'http://localhost:8000/'
phone_number = '01012345678'
password = '12345'
kiosk_id = 'test_kiosk_001'
org_id = 'test_org_001'

saved_session_key = ''


def create_valid_keypoints(count=33):
    """Create valid keypoints array with proper structure"""
    return [
        {
            'x': float(i * 10.0),
            'y': float(i * 15.0),
            'z': float(i * 2.0),
            'visibility': 0.9,
            'presence': 1.0
        }
        for i in range(count)
    ]


def create_valid_body_data():
    """Create valid body data with proper nested structure for AOS API"""
    return {
        'front_data': {
            'results': {
                'shoulder_level_angle': 1.5,
                'hip_level_angle': 2.0,
                'face_level_angle': 1.0,
                'scoliosis_shoulder_ratio': 0.95,
                'scoliosis_hip_ratio': 1.02,
                'leg_length_ratio': 1.01,
                'left_leg_alignment_angle': 178.5,
                'right_leg_alignment_angle': 179.0,
            },
            'keypoints': create_valid_keypoints(33)
        },
        'side_data': {
            'results': {
                'forward_head_angle': 45.0,
                'left_back_knee_angle': 175.0,
                'right_back_knee_angle': 176.0,
            },
            'keypoints': create_valid_keypoints(33)
        },
        'image_front': 'iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAKUlEQVR42u3NMQEAAAgDINc/9IyhBxQgnXYORCwWi8VisVgsFovFf+MF6PxZxcf+kXQAAAAASUVORK5CYII=',
        'image_side': 'iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAKUlEQVR42u3NMQEAAAgDINc/9IyhBxQgnXYORCwWi8VisVgsFovFf+MF6PxZxcf+kXQAAAAASUVORK5CYII=',
        'height': 175.0,
        'weight': 70.0
    }


def create_kiosk_body_data():
    """Create body data for kiosk format (flat structure)"""
    return {
        'face_level_angle': 1.0,
        'shoulder_level_angle': 2.0,
        'hip_level_angle': 3.0,
        'leg_length_ratio': 1.5,
        'left_leg_alignment_angle': 4.0,
        'right_leg_alignment_angle': 5.0,
        'left_back_knee_angle': 6.0,
        'right_back_knee_angle': 7.0,
        'forward_head_angle': 8.0,
        'scoliosis_shoulder_ratio': 1.1,
        'scoliosis_hip_ratio': 1.2,
    }


def create_kiosk_gait_data(session_key):
    """Create gait data for kiosk format (flat structure)"""
    return {
            'session_key': session_key,
            'gait_data': {
                'velocity': 1.0,
                'cadence': 100,
                'cycle_time_l': 0.5,
                'cycle_time_r': 0.5,
                'stride_len_l': 1.0,
                'stride_len_r': 1.0,
                'supp_base_l': 0.1,
                'supp_base_r': 0.1,
                'swing_perc_l': 0.6,
                'swing_perc_r': 0.6,
                'stance_perc_l': 0.4,
                'stance_perc_r': 0.4,
                'd_supp_perc_l': 0.2,
                'd_supp_perc_r': 0.2,
                'toeinout_l': 5,
                'toeinout_r': 5,
                'stridelen_cv_l': 0.01,
                'stridelen_cv_r': 0.01,
                'stridetm_cv_l': 0.01,
                'stridetm_cv_r': 0.01,
                'score': 85
            }
        }


class UrlsTestCase(SimpleTestCase):

    def test_home_url(self):
        url = reverse('home')
        self.assertEqual(resolve(url).func, views.home)

    def test_login_url(self):
        url = reverse('login')
        self.assertEqual(resolve(url).func.view_class, auth_views.LoginView)

    def test_org_register_url(self):
        url = reverse('org_register')
        self.assertEqual(resolve(url).func, views.org_register)

    def test_member_register_url(self):
        url = reverse('member_register')
        self.assertEqual(resolve(url).func, views.member_register)

    def test_report_url(self):
        url = reverse('report')
        self.assertEqual(resolve(url).func, views.report)

    def test_policy_url(self):
        url = reverse('policy')
        self.assertEqual(resolve(url).func, views.policy)

    def test_logout_url(self):
        url = reverse('logout')
        self.assertEqual(resolve(url).func.view_class, auth_views.LogoutView)

    def test_password_change_url(self):
        url = reverse('password_change')
        self.assertEqual(resolve(url).func.view_class, views.CustomPasswordChangeView)

    def test_password_change_done_url(self):
        url = reverse('password_change_done')
        self.assertEqual(resolve(url).func.view_class, auth_views.PasswordChangeDoneView)

    def test_token_obtain_pair_url(self):
        url = reverse('token_obtain_pair')
        self.assertEqual(resolve(url).func.view_class, CustomTokenObtainPairView)

    def test_token_refresh_url(self):
        url = reverse('token_refresh')
        self.assertEqual(resolve(url).func.view_class, CustomTokenRefreshView)

    def test_request_auth_url(self):
        url = reverse('mobile-auth-request_auth')
        self.assertEqual(resolve(url).func, views_mobile.login_mobile)

    def test_login_kiosk_url(self):
        url = reverse('login_kiosk')
        self.assertEqual(resolve(url).func, views_kiosk.login_kiosk)

    def test_login_kiosk_id_url(self):
        url = reverse('login_kiosk_id')
        self.assertEqual(resolve(url).func, views_kiosk.login_kiosk_id)

    def test_get_userinfo_session_url(self):
        url = reverse('get_userinfo_session')
        self.assertEqual(resolve(url).func, views_kiosk.get_userinfo_session)

    def test_end_session_url(self):
        url = reverse('end_session')
        self.assertEqual(resolve(url).func, views_kiosk.end_session)


class LegacyGaitResultTests(TestCase):
    """Legacy gait result tests - updated for better reliability"""
    
    def setUp(self):
        self.kiosk_client = APIClient()
        self.mobile_client = APIClient()
        
        # Use unique identifiers to avoid conflicts
        self.test_mobile_uid = f'legacy_gait_{int(time.time())}'
        self.test_phone_number = '01099887766'
        self.test_password = '1234'
        self.test_kiosk_id = f'legacy_kiosk_{int(time.time())}'
        self.test_org_id = f'legacy_org_{int(time.time())}'

        # Create organization and kiosk
        self.org_info = OrganizationInfo.objects.create(
            organization_name='Legacy Test Organization',
            contact_number='0287654321',
            address='Legacy Test Address',
            institution_type='O'
        )
        
        self.kiosk_info = KioskInfo.objects.create(
            kiosk_id=self.test_kiosk_id,
            Org=self.org_info,
            location='Legacy Test Location'
        )

        # Create the required objects
        self.auth_info = AuthInfo.objects.create(
            uid=self.test_mobile_uid, 
            phone_number=self.test_phone_number
        )
        self.user_info = UserInfo.objects.create(
            username=self.test_phone_number,
            phone_number=self.test_phone_number,
            user_type='O',
            gender='M',
            user_display_name='Legacy Gait Test User'
        )

        # Authenticate and get access token
        auth_response = self.kiosk_client.post('/api/mobile/login-mobile/', 
                                             {'mobile_uid': self.test_mobile_uid}, 
                                             format='json')
        if auth_response.status_code == status.HTTP_200_OK:
            auth_data = auth_response.json()['data']
            self.mobile_client.credentials(HTTP_AUTHORIZATION='Bearer ' + auth_data['jwt_tokens']['access_token'])

            # Login to kiosk and get session key
            response = self.kiosk_client.post('/api/login-kiosk/', 
                                            {'kiosk_id': self.test_kiosk_id}, 
                                            format='json')
            if response.status_code == status.HTTP_200_OK:
                self.session_key = response.data['data']['session_key']

                # Login mobile using QR code
                response = self.mobile_client.post('/api/mobile/login-mobile-qr/', 
                                                 {'session_key': self.session_key},
                                                 format='json')

                # Login kiosk using ID and Password
                response = self.kiosk_client.post('/api/login-kiosk-id/',
                                                {'session_key': self.session_key, 
                                                 'phone_number': self.test_phone_number,
                                                 'password': self.test_password}, 
                                                format='json')

        # Prepare gait data
        self.gait_data = create_kiosk_gait_data(self.session_key)

    def test_get_gait_result_success(self):
        """키오스크에서 보행 분석 결과 생성/조회 테스트"""
        # First, create a gait result
        self.kiosk_client.post(base_url + 'api/analysis/gait/create_result/', self.gait_data, format='json')

        # # Case 1 : using jwt Tokens (mobile)
        # response = self.mobile_client.get(base_url + 'api/analysis/gait/get_result/', {'id': 1}, format='json')
        # self.assertEqual(response.status_code, status.HTTP_200_OK)
        # self.assertIn('data', response.data)
        # self.assertIsInstance(response.data['data'], list)

        # Case 2 : using session_key (kiosk)
        response = self.kiosk_client.get(base_url + 'api/analysis/gait/get_result/?session_key=' + self.session_key)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertIsInstance(response.data['data'], list)

    def test_create_gait_result_missing_session_key(self):
        """세션 키가 없이 보행 분석 결과 생성 테스트"""
        invalid_data = {'gait_data': self.gait_data['gait_data']}  # No session key provided
        response = self.kiosk_client.post(base_url + 'api/analysis/gait/create_result/', invalid_data, format='json')
        self.assertEqual(response.data['data']['message'], 'session_key_required')


class BodyResultTests(TestCase):
    def setUp(self):
        self.kiosk_client = APIClient()
        self.mobile_client = APIClient()

        # Create organization and kiosk first
        self.org_info = OrganizationInfo.objects.create(
            organization_name='Test Organization',
            contact_number='0212345678',
            address='Test Address',
            institution_type='O'
        )
        
        self.kiosk_info = KioskInfo.objects.create(
            kiosk_id=kiosk_id,
            Org=self.org_info,
            location='Test Location'
        )

        # Create the required user
        self.user_info = UserInfo.objects.create(
            username=phone_number,
            phone_number=phone_number,
            password=make_password(password),
            user_type='M'
        )

        # Authenticate and get access token
        auth_response = self.kiosk_client.post('/api/mobile/login-mobile-id/', {'id': phone_number, 'password': password}, format='json')
        auth_data = auth_response.json()
        # print(f"Auth response: {auth_data}")
        
        # Check if login was successful and jwt_tokens exist
        if auth_response.status_code == status.HTTP_200_OK and 'data' in auth_data and 'jwt_tokens' in auth_data['data']:
            self.mobile_client.credentials(HTTP_AUTHORIZATION='Bearer ' + auth_data['data']['jwt_tokens']['access_token'])
        else:
            # Login failed, skip this test
            self.skipTest(f"Login failed: {auth_data}")

        # Login to kiosk and get session key
        response = self.kiosk_client.post('/api/login-kiosk/', {'kiosk_id': kiosk_id}, format='json')
        if response.status_code == status.HTTP_200_OK:
            self.session_key = response.data['data']['session_key']
        else:
            self.skipTest(f"Kiosk login failed: {response.data}")

        # Login mobile using QR code
        response = self.mobile_client.post('/api/mobile/login-mobile-qr/', {'session_key': self.session_key},
                                           format='json')
        if response.status_code != status.HTTP_200_OK:
            self.skipTest(f"Mobile QR login failed: {response.data}")

        # Login kiosk using ID and Password
        response = self.kiosk_client.post('/api/login-kiosk-id/',
                                          {'session_key': self.session_key, 'phone_number': phone_number,
                                           'password': password}, format='json')
        if response.status_code != status.HTTP_200_OK:
            self.skipTest(f"Kiosk ID login failed: {response.data}")

        # Prepare body data
        self.body_data = {
            'session_key': self.session_key,
            'body_data': create_kiosk_body_data(),
            'image_front': 'iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAKUlEQVR42u3NMQEAAAgDINc/9IyhBxQgnXYORCwWi8VisVgsFovFf+MF6PxZxcf+kXQAAAAASUVORK5CYII=',
            'image_side': 'iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAKUlEQVR42u3NMQEAAAgDINc/9IyhBxQgnXYORCwWi8VisVgsFovFf+MF6PxZxcf+kXQAAAAASUVORK5CYII='
        }

    def test_get_body_result_success(self):
        """키오스크에서 체형 분석 결과 생성/조회 테스트"""
        # First, create a body result
        response = self.kiosk_client.post(base_url + 'api/analysis/body/create_result/', self.body_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # # Case 1 : using jwt Tokens (mobile)
        # response = self.mobile_client.get(base_url + 'api/analysis/body/get_result/', {'id': 1}, format='json')
        # self.assertEqual(response.status_code, status.HTTP_200_OK)
        # self.assertIn('data', response.data)
        # self.assertIsInstance(response.data['data'], list)

        # Case 2 : using session_key (kiosk)
        response = self.kiosk_client.get(base_url + 'api/analysis/body/get_result/?session_key=' + self.session_key)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertIsInstance(response.data['data'], list)

    def test_create_body_result_missing_session_key(self):
        """세션 키가 없이 체형 분석 결과 생성 테스트"""
        invalid_data = {'body_data': self.body_data['body_data']}  # No session key provided
        response = self.kiosk_client.post(base_url + 'api/analysis/body/create_result/', invalid_data, format='json')
        self.assertEqual(response.data['data']['message'], 'session_key_required')


class mobileTest(TestCase):
    def setUp(self):
        self.client = APIClient()

        # Create the required objects
        self.user_info = UserInfo.objects.create(
            username=phone_number,
            phone_number=phone_number,
            password=make_password(password),
            user_type='O'
        )

        # Authenticate and get access token
        auth_response = self.client.post('/api/mobile/login-mobile-id/', {'id': phone_number, 'password': password},
                                         format='json')
        auth_data = auth_response.json()['data']
        self.assertEqual(auth_response.status_code, status.HTTP_200_OK) and self.assertIn('jwt_tokens', auth_data)
        self.client.credentials(HTTP_AUTHORIZATION='Bearer ' + auth_data['jwt_tokens']['access_token'])

    def test_get_user_success(self):
        """모바일 유저 정보 조회 테스트"""
        response = self.client.post(base_url + 'api/mobile/user/get_user/', format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertIsInstance(response.data['data'], dict)

    def test_delete_user_success(self):
        """모바일 유저 삭제 테스트"""
        response = self.client.post(base_url + 'api/mobile/user/delete_user/', format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)


class UserScenarioTest(TestCase):
    """
    실제 사용자 유스케이스(회원가입, 로그인, 분석 생성/조회, 가족 회원 등)를
    모바일/키오스크/AOS/기관유저/가족회원 기준으로 end-to-end 테스트합니다.
    """
    def setUp(self):
        self.client = APIClient()
        self.base_url = '/api/'
        self.mobile_phone = '01011112222'
        self.mobile_password = 'testpass1!'
        self.kiosk_id = 'testkiosk001'
        self.aos_phone = '01033334444'
        self.aos_password = 'aospass!'
        self.org_name = '테스트기관'
        self.family_phone = '01055556666'
        self.family_password = 'familypass!'

    def mobile_signup_and_login(self):
        # 회원가입
        signup_data = {
            'phone_number': self.mobile_phone,
            'password': self.mobile_password,
            'dob': '1990',
            'gender': '0',  # 남자
        }
        resp = self.client.post(self.base_url + 'mobile/auth/signup/', signup_data, format='json')
        self.assertEqual(resp.status_code, 200)
        # 로그인
        login_data = {'id': self.mobile_phone, 'password': self.mobile_password}
        resp = self.client.post(self.base_url + 'mobile/login-mobile-id/', login_data, format='json')
        self.assertEqual(resp.status_code, 200)
        access_token = resp.json()['data']['jwt_tokens']['access_token']
        return access_token

    def test_mobile_user_flow(self):
        """모바일 유저 전체 플로우: 회원가입부터 분석까지"""
        access_token = self.mobile_signup_and_login()
        self.client.credentials(HTTP_AUTHORIZATION='Bearer ' + access_token)
        # 체형분석 생성 (v2 포맷)
        front_keypoints = [
            {"x": float(i), "y": float(i+1), "z": float(i+2), "visibility": 0.9, "presence": 1.0}
            for i in range(33)
        ]
        side_keypoints = [
            {"x": float(i+0.5), "y": float(i****), "z": float(i****), "visibility": 0.8, "presence": 0.95}
            for i in range(33)
        ]
        self.body_data = {
            'front_data': {
                'results': {
                    'shoulder_level_angle': 1.0,
                    'hip_level_angle': 2.0,
                    'face_level_angle': 3.0,
                    'scoliosis_shoulder_ratio': 4.0,
                    'scoliosis_hip_ratio': 5.0,
                    'leg_length_ratio': 6.0,
                    'left_leg_alignment_angle': 7.0,
                    'right_leg_alignment_angle': 8.0,
                },
                'keypoints': front_keypoints
            },
            'side_data': {
                'results': {
                    'forward_head_angle': 1.1,
                    'left_back_knee_angle': 2.2,
                    'right_back_knee_angle': 3.3
                },
                'keypoints': side_keypoints
            },
            'image_front': 'iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAKUlEQVR42u3NMQEAAAgDINc/9IyhBxQgnXYORCwWi8VisVgsFovFf+MF6PxZxcf+kXQAAAAASUVORK5CYII=',
            'image_side': 'iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAKUlEQVR42u3NMQEAAAgDINc/9IyhBxQgnXYORCwWi8VisVgsFovFf+MF6PxZxcf+kXQAAAAASUVORK5CYII=',
            'height': 170.0,
            'weight': 65.0,
        }
        resp = self.client.post(self.base_url + 'v2/mobile/body/create_body_result/', self.body_data, format='json')
        self.assertEqual(resp.status_code, 200)
        # 체형분석 조회
        resp = self.client.get(self.base_url + 'v2/mobile/body/get_body_result/', format='json')
        self.assertEqual(resp.status_code, 200)


        # 가족회원 생성
        family_data = {
            'family_member_name': '아빠',
            'gender': 'M',
            'relationship': '부',
        }
        resp = self.client.post(self.base_url + 'v2/mobile/family/create_family_user/', family_data, format='json')
        self.assertEqual(resp.status_code, 201)
        # 가족회원 목록 조회
        resp = self.client.get(self.base_url + 'v2/mobile/family/get_family_user/', format='json')
        self.assertEqual(resp.status_code, 200)

        self.family_member_id = resp.json()['data']['family_users'][0]['id']  # 생성된 가족회원 ID

        """ 가족회원으로 체형 분석 생성 및 조회 """
        family_member_id = resp.json()['data']['family_users'][0]['id'] # 생성된 가족회원 ID
        self.body_data['family_user_id'] = family_member_id # 체형분석결과에 가족회원 ID 추가
        resp = self.client.post(self.base_url + 'v2/mobile/body/create_body_result/', self.body_data, format='json')
        self.assertEqual(resp.status_code, 200)
        
        # 체형분석 조회
        resp = self.client.get(self.base_url + f'v2/mobile/body/get_body_result/?page=1&page_size=10&mobile=y&family_user_id={self.family_member_id}', format='json')
        self.assertEqual(resp.json()['data'][0]['family_user'], self.family_member_id)


class ComprehensiveMobileFlowTests(APITransactionTestCase):
    """
    Comprehensive test suite for Mobile platform covering complete user flow:
    Signup → Login → Body Analysis → Gait Analysis → Family Management
    """
    
    def setUp(self):
        """Set up test data and client"""
        self.client = APIClient()
        self.maxDiff = None
        
        # Test data
        self.mobile_uid = f'mobile_test_{int(time.time())}'
        self.phone_number = '01098765432'
        self.password = '1234'
        self.kiosk_id = f'kiosk_test_{int(time.time())}'
        
        # Sample body analysis data using correct format
        self.body_data = create_valid_body_data()
        
        # Sample gait analysis data
        self.gait_data = create_kiosk_gait_data('null')

    def test_complete_mobile_user_flow(self):
        """모바일 유저 전체 플로우: 회원가입부터 체형/보행 분석"""
        
        # Step 1: Mobile Signup
        signup_data = {
            'phone_number': self.phone_number,
            'password': self.password,
            'gender': '0',  # 0: M, 1: F
            'dob': '1990',
            'name': 'Test User'
        }
        
        response = self.client.post('/api/mobile/auth/signup/', signup_data, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['message'], 'success')
        
        AuthInfo.objects.create(
            uid=self.mobile_uid,
            phone_number=self.phone_number
        )

        # Step 2 - 1: Kiosk Login (Create Session)
        response = self.client.post('/api/login-kiosk/', {'kiosk_id': self.kiosk_id}, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('session_key', response.data['data'])
        session_key = response.data['data']['session_key']

        self.gait_data['session_key'] = session_key

        # Step 2- 2: Mobile Login
        login_data = {'mobile_uid': self.mobile_uid}
        response = self.client.post('/api/mobile/login-mobile/', login_data, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('jwt_tokens', response.data['data'])
        
        # Set JWT token for authenticated requests
        access_token = response.data['data']['jwt_tokens']['access_token']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # Step 2 - 3: Mobile QR Login (Link Mobile to Kiosk Session)
        qr_data = {'session_key': session_key}
        response = self.client.post('/api/mobile/login-mobile-qr/', qr_data, format='json')
        self.assertEqual(response.status_code, 200)

        # Step 2 - 4: Gait Analysis Creation (required before retrieval)
        response = self.client.post('/api/analysis/gait/create_result/', self.gait_data, format='json')
        if response.status_code not in [200, 201]:
            print(f"Gait creation failed with status {response.status_code}: {response.content}")
        self.assertIn(response.status_code, [200, 201])

        
        # Step 3: Body Analysis Creation (using AOS endpoint)
        response = self.client.post('/api/v2/mobile/body/create_body_result/', self.body_data, format='json')
        if response.status_code not in [200, 201]:
            print(f"Body creation failed with status {response.status_code}: {response.content}")
        self.assertIn(response.status_code, [200, 201])
        
        # Step 4: Body Analysis Retrieval (using AOS endpoint with mobile filter)
        response = self.client.get('/api/v2/mobile/body/get_body_result/?mobile=y', format='json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('data', response.data)
                
        # Step 5: Gait Analysis Retrieval
        response = self.client.get('/api/mobile/gait/get_gait_result/', format='json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('data', response.data)
        
        # Step 7: Family Member Management
        family_data = {
            'family_member_name': 'Family Member',
            'gender': 'F',
            'relationship': '배우자'
        }
        
        response = self.client.post('/api/v2/mobile/family/create_family_user/', family_data, format='json')
        self.assertIn(response.status_code, [200, 201])
        
        # Step 8: Get Family Members
        response = self.client.get('/api/v2/mobile/family/get_family_user/', format='json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('data', response.data)
        
        # Step 9: Create Body Analysis for Family Member
        if response.data['data'].get('family_users'):
            family_member_id = response.data['data']['family_users'][0]['id']
            family_body_data = self.body_data.copy()
            family_body_data['family_user_id'] = family_member_id
            
            response = self.client.post('/api/v2/mobile/body/create_body_result/', family_body_data, format='json')
            self.assertIn(response.status_code, [200, 201])


class ComprehensiveKioskFlowTests(APITransactionTestCase):
    """
    Comprehensive test suite for Kiosk platform covering complete user flow:
    Kiosk Setup → User Login → Body/Gait Analysis → Session Management
    """
    
    def setUp(self):
        """Set up test data and clients"""
        self.client = APIClient()
        self.mobile_client = APIClient()
        self.maxDiff = None
        
        # Test data
        self.kiosk_id = f'kiosk_test_{int(time.time())}'
        self.mobile_uid = f'kiosk_mobile_{int(time.time())}'
        self.phone_number = '01087654321'
        self.password = 'kiosktest123'
        self.org_id = f'org_test_{int(time.time())}'
        
        # Create required organization
        self.org_info = OrganizationInfo.objects.create(
            organization_name='Test Organization',
            contact_number='0212345678',
            address='Test Address',
            institution_type='H'
        )
        
        # Create kiosk info
        self.kiosk_info = KioskInfo.objects.create(
            kiosk_id=self.kiosk_id,
            Org=self.org_info,
            location='Test Location'
        )
        
        # Create auth info for mobile user
        self.auth_info = AuthInfo.objects.create(
            uid=self.mobile_uid,
            phone_number=self.phone_number
        )
        
        # Create user info
        self.user_info = UserInfo.objects.create(
            username=self.phone_number,
            phone_number=self.phone_number,
            password=make_password(self.password),
            user_type='M',
            gender='M',
            user_display_name='Kiosk Test User'
        )
        
        # Sample analysis data (using kiosk format with flat structure)
        self.body_data = create_kiosk_body_data()
        
        self.gait_data = {
            'velocity': 1.3,
            'cadence': 115,
            'cycle_time_l': 1.0,
            'cycle_time_r': 1.1,
            'stride_len_l': 1.2,
            'stride_len_r': 1.1,
            'supp_base_l': 0.15,
            'supp_base_r': 0.16,
            'swing_perc_l': 38.0,
            'swing_perc_r': 39.0,
            'stance_perc_l': 62.0,
            'stance_perc_r': 61.0,
            'd_supp_perc_l': 18.0,
            'd_supp_perc_r': 19.0,
            'toeinout_l': 3.0,
            'toeinout_r': 4.0,
            'stridelen_cv_l': 0.12,
            'stridelen_cv_r': 0.11,
            'stridetm_cv_l': 0.15,
            'stridetm_cv_r': 0.14,
            'score': 88.0,
        }

    def test_complete_kiosk_flow(self):
        """키오스크 전체 플로우: 키오스크 로그인 → 모바일 로그인 → 분석 생성/조회"""
        
        # Step 2: Kiosk Login (Create Session)
        response = self.client.post('/api/login-kiosk/', {'kiosk_id': self.kiosk_id}, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('session_key', response.data['data'])
        session_key = response.data['data']['session_key']
        
        # Step 3: Mobile Login with JWT
        mobile_login_data = {'mobile_uid': self.mobile_uid}
        response = self.mobile_client.post('/api/mobile/login-mobile/', mobile_login_data, format='json')
        self.assertEqual(response.status_code, 200)
        access_token = response.data['data']['jwt_tokens']['access_token']
        self.mobile_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # Step 4: Mobile QR Login (Link Mobile to Kiosk Session)
        qr_data = {'session_key': session_key}
        response = self.mobile_client.post('/api/mobile/login-mobile-qr/', qr_data, format='json')
        self.assertEqual(response.status_code, 200)
        
        # Step 5: Kiosk ID Login (Alternative login method)
        id_login_data = {
            'session_key': session_key,
            'phone_number': self.phone_number,
            'password': self.password
        }
        response = self.client.post('/api/login-kiosk-id/', id_login_data, format='json')
        self.assertEqual(response.status_code, 200)
        
        # Step 6: Get User Info by Session
        response = self.client.get(f'/api/get-userinfo-session/?session_key={session_key}')
        self.assertEqual(response.status_code, 200)
        self.assertIn('data', response.data)
        
        # Step 7: Body Analysis Creation via Kiosk
        body_request_data = {
            'session_key': session_key,
            **self.body_data
        }
        # response = self.client.post('/api/analysis/body/create_result/', body_request_data, format='json')
        # if response.status_code not in [200, 201]:
        #     print(f"Kiosk body creation failed with status {response.status_code}: {response.content}")
        # self.assertIn(response.status_code, [200, 201])
        
        # Step 8: Body Analysis Retrieval
        response = self.client.get(f'/api/analysis/body/get_result/?session_key={session_key}')
        self.assertEqual(response.status_code, 200)
        
        # Step 9: Gait Analysis Creation via Kiosk
        gait_request_data = {
            'session_key': session_key,
            'gait_data': self.gait_data
        }
        response = self.client.post('/api/analysis/gait/create_result/', gait_request_data, format='json')
        self.assertIn(response.status_code, [200, 201])
        
        # Step 10: Gait Analysis Retrieval
        response = self.client.get(f'/api/analysis/gait/get_result/?session_key={session_key}')
        self.assertEqual(response.status_code, 200)
        
        # Step 11: End Session
        end_session_data = {'session_key': session_key}
        response = self.client.post('/api/end-session/', end_session_data, format='json')
        self.assertEqual(response.status_code, 200)


class ComprehensiveAOSFlowTests(APITransactionTestCase):
    """
    Comprehensive test suite for AOS (Analysis) platform covering:
    JWT Authentication → Body/Gait Analysis → Data Retrieval
    """
    
    def setUp(self):
        """Set up test data and client for AOS testing"""
        self.client = APIClient()
        self.maxDiff = None
        
        # Test data
        self.mobile_uid = f'aos_test_{int(time.time())}'
        self.phone_number = '01076543210'
        self.password = 'aostest123'
        
        # Create auth and user info
        self.auth_info = AuthInfo.objects.create(
            uid=self.mobile_uid,
            phone_number=self.phone_number
        )
        
        self.user_info = UserInfo.objects.create(
            username=self.phone_number,
            phone_number=self.phone_number,
            password=make_password(self.password),
            user_type='M',
            gender='F',
            user_display_name='AOS Test User'
        )
        
        # Analysis data (using proper AOS format with nested structure)
        self.body_analysis_data = create_valid_body_data()
        
        self.gait_analysis_data = {
            'velocity': 1.1,
            'cadence': 105,
            'cycle_time_l': 1.2,
            'cycle_time_r': 1.1,
            'stride_len_l': 0.95,
            'stride_len_r': 0.98,
            'supp_base_l': 0.18,
            'supp_base_r': 0.17,
            'swing_perc_l': 42.0,
            'swing_perc_r': 41.5,
            'stance_perc_l': 58.0,
            'stance_perc_r': 58.5,
            'd_supp_perc_l': 22.0,
            'd_supp_perc_r': 21.5,
            'toeinout_l': 6.0,
            'toeinout_r': 5.5,
            'stridelen_cv_l': 0.13,
            'stridelen_cv_r': 0.12,
            'stridetm_cv_l': 0.18,
            'stridetm_cv_r': 0.17,
            'score': 82.0,
        }

    def test_complete_aos_flow(self):
        """AOS 전체 플로우 : 로그인 -> 체형/보행 분석 생성 및 조회"""
        
        # Step 1: Authenticate and get JWT token
        login_data = {'mobile_uid': self.mobile_uid}
        response = self.client.post('/api/mobile/login-mobile/', login_data, format='json')
        self.assertEqual(response.status_code, 200)
        
        access_token = response.data['data']['jwt_tokens']['access_token']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # Step 2: Create Body Analysis via AOS API
        response = self.client.post('/api/v2/mobile/body/create_body_result/', self.body_analysis_data, format='json')
        self.assertIn(response.status_code, [200, 201])
        
        # Step 3: Retrieve Body Analysis Results
        response = self.client.get('/api/v2/mobile/body/get_body_result/?mobile=y')
        self.assertEqual(response.status_code, 200)
        self.assertIn('data', response.data)
        
        # Step 4: Create Gait Analysis via AOS API
        gait_request_data = {'gait_data': self.gait_analysis_data}
        response = self.client.post('/api/analysis/gait/create_result/', gait_request_data, format='json')
        self.assertIn(response.status_code, [200, 201])
        
        # Step 5: Retrieve Gait Analysis Results
        response = self.client.get('/api/analysis/gait/get_result/')
        self.assertEqual(response.status_code, 200)
        self.assertIn('data', response.data)

    def test_aos_batch_analysis(self):
        """AOS 체형 분석 결과 생성"""
        
        # Authenticate
        login_data = {'mobile_uid': self.mobile_uid}
        response = self.client.post('/api/mobile/login-mobile/', login_data, format='json')
        access_token = response.data['data']['jwt_tokens']['access_token']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # Create multiple body analyses
        for i in range(3):
            body_data = self.body_analysis_data.copy()
            # Update height/weight in the results section
            body_data['front_data']['results']['height'] = 165.0 + i
            body_data['front_data']['results']['weight'] = 60.0 + i
            
            response = self.client.post('/api/v2/mobile/body/create_body_result/', body_data, format='json')
            self.assertIn(response.status_code, [200, 201])
        
        # Create multiple gait analyses
        for i in range(3):
            gait_data = {'gait_data': self.gait_analysis_data.copy()}
            gait_data['gait_data']['velocity'] = 1.1 + (i * 0.1)
            
            response = self.client.post('/api/analysis/gait/create_result/', gait_data, format='json')
            self.assertIn(response.status_code, [200, 201])
        
        # Verify all analyses were created
        response = self.client.get('/api/v2/mobile/body/get_body_result/?mobile=y')
        self.assertEqual(response.status_code, 200)
        
        response = self.client.get('/api/analysis/gait/get_result/')
        self.assertEqual(response.status_code, 200)


class CrossPlatformIntegrationTests(APITransactionTestCase):
    """
    Integration tests covering cross-platform scenarios:
    Mobile → Kiosk integration, Data consistency across platforms
    """
    
    def setUp(self):
        """Set up test data for cross-platform testing"""
        self.mobile_client = APIClient()
        self.kiosk_client = APIClient()
        self.aos_client = APIClient()
        self.maxDiff = None
        
        # Shared test data
        self.mobile_uid = f'cross_test_{int(time.time())}'
        self.phone_number = '01065432109'
        self.password = 'crosstest123'
        self.kiosk_id = f'cross_kiosk_{int(time.time())}'
        self.org_id = f'cross_org_{int(time.time())}'
        
        # Create organization and kiosk
        self.org_info = OrganizationInfo.objects.create(
            organization_name='Cross Platform Test Org',
            contact_number='0298765432',
            address='Cross Test Address',
            institution_type='C'
        )
        
        self.kiosk_info = KioskInfo.objects.create(
            kiosk_id=self.kiosk_id,
            Org=self.org_info,
            location='Cross Test Location'
        )
        
        # Create user data
        self.auth_info = AuthInfo.objects.create(
            uid=self.mobile_uid,
            phone_number=self.phone_number
        )
        
        self.user_info = UserInfo.objects.create(
            username=self.phone_number,
            phone_number=self.phone_number,
            password=make_password(self.password),
            user_type='M',
            gender='M',
            user_display_name='Cross Platform User'
        )

    def test_mobile_to_kiosk_data_consistency(self):
        """모바일과 키오스크 데이터 일관성 검증"""
        
        # Step 1: Create data via Mobile
        login_data = {'mobile_uid': self.mobile_uid}
        response = self.mobile_client.post('/api/mobile/login-mobile/', login_data, format='json')
        access_token = response.data['data']['jwt_tokens']['access_token']
        self.mobile_client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # Create body analysis via mobile
        body_data = create_valid_body_data()
        
        response = self.mobile_client.post('/api/v2/mobile/body/create_body_result/', body_data, format='json')
        self.assertIn(response.status_code, [200, 201])
        
        # Get data via mobile
        response = self.mobile_client.get('/api/v2/mobile/body/get_body_result/?mobile=y')
        mobile_body_data = response.data
        
        # Step 2: Access same data via Kiosk
        # Login to kiosk
        response = self.kiosk_client.post('/api/login-kiosk/', {'kiosk_id': self.kiosk_id}, format='json')
        session_key = response.data['data']['session_key']
        
        # Link mobile user to kiosk session
        qr_data = {'session_key': session_key}
        response = self.mobile_client.post('/api/mobile/login-mobile-qr/', qr_data, format='json')
        self.assertEqual(response.status_code, 200)
        
        # Get data via kiosk
        response = self.kiosk_client.get(f'/api/analysis/body/get_result/?session_key={session_key}')
        kiosk_body_data = response.data
        
        # Step 3: Verify data consistency
        self.assertEqual(response.status_code, 200)
        self.assertIn('data', kiosk_body_data)

    def test_complete_cross_platform_workflow(self):
        """크로스 플랫폼 전체 워크플로우 검증"""
        
        # Mobile Phase
        login_data = {'mobile_uid': self.mobile_uid}
        response = self.mobile_client.post('/api/mobile/login-mobile/', login_data, format='json')
        mobile_token = response.data['data']['jwt_tokens']['access_token']
        self.mobile_client.credentials(HTTP_AUTHORIZATION=f'Bearer {mobile_token}')
        
        # Kiosk Phase
        response = self.kiosk_client.post('/api/login-kiosk/', {'kiosk_id': self.kiosk_id}, format='json')
        session_key = response.data['data']['session_key']
        
        # Link Mobile to Kiosk
        response = self.mobile_client.post('/api/mobile/login-mobile-qr/', {'session_key': session_key}, format='json')
        self.assertEqual(response.status_code, 200)
        
        # AOS Phase - Use same mobile token
        self.aos_client.credentials(HTTP_AUTHORIZATION=f'Bearer {mobile_token}')
        
        # Create analyses across platforms and verify consistency
        # This demonstrates the integrated nature of the system
        
        # Create via AOS
        body_data = create_valid_body_data()
        
        response = self.aos_client.post('/api/v2/mobile/body/create_body_result/', body_data, format='json')
        self.assertIn(response.status_code, [200, 201])
        
        # Verify via Mobile
        response = self.mobile_client.get('/api/v2/mobile/body/get_body_result/')
        self.assertEqual(response.status_code, 200)
        
        # Verify via Kiosk
        response = self.kiosk_client.get(f'/api/analysis/body/get_result/?session_key={session_key}')
        self.assertEqual(response.status_code, 200)


class ErrorHandlingAndEdgeCaseTests(APITransactionTestCase):
    """
    Test error handling and edge cases across all platforms
    """
    
    def setUp(self):
        self.client = APIClient()
        self.mobile_uid = f'error_test_{int(time.time())}'
        self.phone_number = '01054321098'
        
        # Create minimal user data
        self.auth_info = AuthInfo.objects.create(
            uid=self.mobile_uid,
            phone_number=self.phone_number
        )

    def test_invalid_authentication(self):
        """잘못된 인증 시나리오 테스트"""
        
        # Invalid mobile UID
        response = self.client.post('/api/mobile/login-mobile/', {'mobile_uid': 'invalid_uid'}, format='json')
        self.assertNotEqual(response.data['data']['status'], 200)
        
        # Invalid session key
        response = self.client.get('/api/analysis/body/get_result/?session_key=invalid_session')
        self.assertNotEqual(response.data['data']['status'], 200)
        
        # Invalid JWT token
        self.client.credentials(HTTP_AUTHORIZATION='Bearer invalid_token')
        response = self.client.get('/api/v2/mobile/body/get_body_result/')
        self.assertNotEqual(response.status_code, 200)

    def test_missing_required_fields(self):
        """필수 필드 누락 처리 테스트"""
        
        # Login first
        response = self.client.post('/api/mobile/login-mobile/', {'mobile_uid': self.mobile_uid}, format='json')
        if response.status_code == 200:
            token = response.data['data']['jwt_tokens']['access_token']
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            
            # Test missing keypoints
            incomplete_body_data = {
                'height': 175.0,
                'weight': 70.0
                # Missing front_data and side_data required for AOS API
            }
            
            response = self.client.post('/api/v2/mobile/body/create_body_result/', incomplete_body_data, format='json')
            self.assertNotEqual(response.status_code, 200)

    def test_invalid_data_formats(self):
        """잘못된 데이터 포맷 처리 테스트"""
        
        # Login first
        response = self.client.post('/api/mobile/login-mobile/', {'mobile_uid': self.mobile_uid}, format='json')
        if response.status_code == 200:
            token = response.data['data']['jwt_tokens']['access_token']
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            
            # Test invalid keypoint format
            invalid_body_data = {
                'front_data': 'invalid_format',  # Should be nested object
                'side_data': {'results': {}, 'keypoints': create_valid_keypoints()},
                'height': 'invalid_height',  # Should be number
                'weight': 70.0
            }
            
            response = self.client.post('/api/v2/mobile/body/create_body_result/', invalid_body_data, format='json')
            self.assertNotEqual(response.status_code, 200)


# class PerformanceAndLoadTests(APITransactionTestCase):
#     """
#     Performance and load testing for critical endpoints
#     """
    
#     def setUp(self):
#         self.client = APIClient()
#         self.mobile_uid = f'perf_test_{int(time.time())}'
#         self.phone_number = '01043210987'
        
#         # Create user data
#         self.auth_info = AuthInfo.objects.create(
#             uid=self.mobile_uid,
#             phone_number=self.phone_number
#         )
        
#         self.user_info = UserInfo.objects.create(
#             username=self.phone_number,
#             phone_number=self.phone_number,
#             password=make_password('perftest123'),
#             user_type='M',
#             gender='M',
#             user_display_name='Performance Test User'
#         )

#     def test_concurrent_analysis_creation(self):
#         """동시 분석 생성 성능 테스트"""
        
#         # Login and get token
#         response = self.client.post('/api/mobile/login-mobile/', {'mobile_uid': self.mobile_uid}, format='json')
#         if response.status_code == 200:
#             token = response.data['data']['jwt_tokens']['access_token']
#             self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            
#             # Create multiple analyses rapidly
#             body_data = create_valid_body_data()
            
#             start_time = time.time()
            
#             # Create 5 body analyses
#             for i in range(5):
#                 response = self.client.post('/api/v2/mobile/body/create_body_result/', body_data, format='json')
#                 # Don't assert success for performance test, just measure
            
#             end_time = time.time()
#             execution_time = end_time - start_time
            
#             # Performance assertion - should complete within reasonable time
#             self.assertLess(execution_time, 10.0, "Analysis creation took too long")

#     def test_large_data_handling(self):
#         """대용량 데이터 처리 테스트"""
        
#         # Login
#         response = self.client.post('/api/mobile/login-mobile/', {'mobile_uid': self.mobile_uid}, format='json')
#         if response.status_code == 200:
#             token = response.data['data']['jwt_tokens']['access_token']
#             self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            
#             # Test with large keypoint arrays - but keep proper structure
#             large_body_data = create_valid_body_data()
#             # Add some large image data to test size handling
#             large_body_data['front_data']['results']['image_front'] = 'large_image_data' * 100
#             large_body_data['side_data']['results']['image_side'] = 'large_image_data' * 100
            
#             response = self.client.post('/api/v2/mobile/body/create_body_result/', large_body_data, format='json')
#             # The system should handle large data gracefully