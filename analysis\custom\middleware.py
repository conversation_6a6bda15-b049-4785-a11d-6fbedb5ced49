import os
import json
from datetime import datetime
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent.parent

### desc: 실제 IP를 확인할 수 있는 func
def get_client_ip(request):
    # X-Real-IP 가 있을 때 우선 사용, 없으면 X-Forwarded-For 첫 번째, 없으면 REMOTE_ADDR
    ip = request.META.get('HTTP_X_REAL_IP')
    if not ip:
        xff = request.META.get('HTTP_X_FORWARDED_FOR')
        if xff:
            ip = xff.split(',')[0].strip()
    if not ip:
        ip = request.META.get('REMOTE_ADDR')
    return ip

class RequestLoggingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        # 로깅에서 제외할 URL 패턴들
        self.exclude_patterns = [
            '/metrics',  # Prometheus metrics
            '/sentry',   # Sentry 관련
            '/health',   # Health check
            '/favicon.ico',  # 파비콘
            '/static/',  # 정적 파일
            '/admin/jsi18n/',  # Django admin i18n
        ]

    def should_log_request(self, request_path):
        """요청을 로깅할지 결정하는 메서드"""
        for pattern in self.exclude_patterns:
            if request_path.startswith(pattern):
                return False
        return True

    def __call__(self, request):
        response = self.get_response(request)
        
        # 특정 패턴의 요청은 로깅하지 않음
        if not self.should_log_request(request.path):
            return response
            
        # 로그 파일명: 하루 단위로 분리
        log_dir = os.path.join(os.path.join(f'{BASE_DIR}/logs'))
        os.makedirs(log_dir, exist_ok=True)
        logfile = os.path.join(
            log_dir,
            f'request_log_{datetime.now().strftime("%Y%m%d")}.jsonl'
        )

        # 로그 데이터 구성
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "url": request.path,
            "ip": get_client_ip(request),
            "status": response.status_code,
            "user_id": getattr(request.user, 'id', None) if getattr(request, 'user', None) and request.user.is_authenticated else None
        }

        # JSON 로그 파일에 한 줄씩 기록
        with open(logfile, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_data, ensure_ascii=False) + '\n')

        return response