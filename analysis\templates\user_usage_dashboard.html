{% extends 'base.html' %}
{% load static %}

{% block title %}사용량 조회{% endblock %}

{% block content %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
        position: relative;
        overflow-x: hidden;
    }
    
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: 
            radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%),
            radial-gradient(circle at 50% 50%, rgba(255,255,255,0.05) 0%, transparent 50%);
        animation: particles 20s linear infinite;
        z-index: -1;
        pointer-events: none;
    }
    
    @keyframes particles {
        0% { transform: translateY(0) rotate(0deg); }
        100% { transform: translateY(-100vh) rotate(360deg); }
    }

    .usage-dashboard {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
        min-height: 100vh;
        position: relative;
    }

    .dashboard-header {
        background: linear-gradient(234deg, rgb(246 199 49 / 20%) 0%, rgb(170 141 141 / 10%) 100%);
        backdrop-filter: blur(20px);
        color: white;
        padding: 40px;
        border-radius: 20px;
        margin-bottom: 40px;
        text-align: center;
        box-shadow: 
            0 20px 40px rgba(0,0,0,0.2),
            inset 0 1px 0 rgba(255,255,255,0.2);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(255,255,255,0.1);
    }
    
    .dashboard-header h1 {
        margin: 0 0 15px 0;
        font-size: 3em;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        position: relative;
        color: #333;
        z-index: 2;
    }
    
    .dashboard-header p {
        margin: 0;
        opacity: 0.95;
        font-size: 1.3em;
        font-weight: 300;
        position: relative;
        color: #555;
        z-index: 2;
    }

    .filter-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 40px;
        box-shadow: 
            0 15px 35px rgba(0,0,0,0.1),
            inset 0 1px 0 rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.1);
    }
    
    .filter-section h3 {
        margin: 0 0 25px 0;
        color: #333;
        font-size: 1.5em;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .filter-section h3::before {
        content: '🔍';
        font-size: 1.2em;
    }

    .form-row {
        display: flex;
        gap: 20px;
        align-items: end;
        flex-wrap: wrap;
    }

    .form-group {
        flex: 1;
        min-width: 200px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #333;
        font-weight: 600;
        font-size: 14px;
    }

    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid rgba(255,255,255,0.3);
        border-radius: 10px;
        background: rgba(255,255,255,0.9);
        font-size: 14px;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: rgba(255,255,255,1);
    }

    .btn {
        padding: 12px 25px;
        border: none;
        border-radius: 10px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        min-height: 46px;
        box-sizing: border-box;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(116, 185, 255, 0.4);
    }

    .alert {
        padding: 15px 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .alert-success {
        background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
    }

    .alert-error {
        background: linear-gradient(135deg, #e17055 0%, #d63031 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(225, 112, 85, 0.3);
    }

    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }

    .summary-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.1);
        transition: all 0.3s ease;
    }

    .summary-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }

    .summary-card h4 {
        margin: 0 0 15px 0;
        color: #333;
        font-size: 1.2em;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .summary-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .stat-item {
        text-align: center;
        padding: 15px;
        background: rgba(255,255,255,0.5);
        border-radius: 10px;
    }

    .stat-number {
        display: block;
        font-size: 1.8em;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.9em;
        color: #666;
        font-weight: 500;
    }

    .data-table-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 40px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.1);
    }

    .data-table-section h3 {
        margin: 0 0 25px 0;
        color: #333;
        font-size: 1.5em;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .data-table-section h3::before {
        content: '📊';
        font-size: 1.2em;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        background: white;
    }

    .data-table th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
        padding: 20px 15px;
        text-align: center;
        font-weight: 700;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 3px solid #667eea;
    }

    .data-table td {
        padding: 18px 15px;
        border-bottom: 1px solid #f0f0f0;
        color: #555;
        font-weight: 500;
        text-align: center;
        transition: all 0.3s ease;
    }

    .data-table tbody tr {
        transition: all 0.3s ease;
        position: relative;
    }

    .data-table tbody tr:hover {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        transform: scale(1.01);
        box-shadow: 
            0 8px 25px rgba(102, 126, 234, 0.15),
            inset 0 1px 0 rgba(255,255,255,0.2);
        position: relative;
        z-index: 1;
    }

    .data-table tbody tr:last-child td {
        border-bottom: none;
    }

    .no-data {
        text-align: center;
        padding: 80px 30px;
        color: #6c757d;
        font-size: 18px;
        font-weight: 500;
    }

    .no-data::before {
        content: '📊';
        display: block;
        font-size: 48px;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    .error-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        padding: 40px;
        border-radius: 20px;
        text-align: center;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.1);
    }

    .error-section h3 {
        color: #e74c3c;
        font-size: 1.5em;
        margin-bottom: 15px;
    }

    .error-section p {
        color: #666;
        font-size: 1.1em;
    }

    .redis-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 40px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.1);
    }

    .redis-section h3 {
        margin: 0 0 25px 0;
        color: #333;
        font-size: 1.5em;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .redis-section h3::before {
        content: '⚡';
        font-size: 1.2em;
    }

    .redis-sync-form {
        margin-bottom: 20px;
    }

    .form-errors {
        background: rgba(231, 76, 60, 0.1);
        border: 1px solid #e74c3c;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .form-errors ul {
        margin: 0;
        padding-left: 20px;
        color: #e74c3c;
    }

    .form-errors li {
        margin-bottom: 5px;
        font-weight: 500;
    }

    @media (max-width: 768px) {
        .form-row {
            flex-direction: column;
        }
        
        .form-group {
            min-width: 100%;
        }
        
        .summary-cards {
            grid-template-columns: 1fr;
        }
        
        .data-table {
            font-size: 12px;
        }
        
        .data-table th,
        .data-table td {
            padding: 10px 8px;
        }
    }
</style>

<div class="usage-dashboard">
    <!-- 헤더 -->
    <div class="dashboard-header">
        <h1>키오스크 사용량 조회</h1>
        <p>
            {% if user_organization %}
                {{ user_organization.name }} - 실시간 사용량 통계
            {% else %}
                키오스크 사용량 통계
            {% endif %}
        </p>
    </div>

    <!-- 오류 처리 -->
    {% if no_organization %}
        <div class="error-section">
            <h3>기관 정보 없음</h3>
            <p>{{ error_message }}</p>
        </div>
        {% else %}

    <!-- 동기화 메시지 -->
    {% if sync_message %}
        <div class="alert alert-{{ sync_message.type }}">
            <i class="fas fa-check-circle"></i>
            {{ sync_message.text }}
        </div>
    {% endif %}

    <!-- 검색 폼 -->
    <div class="filter-section">
        <h3>조회 조건 설정</h3>
        
        {% if form.errors %}
            <div class="form-errors">
                <ul>
                    {% for field in form %}
                        {% for error in field.errors %}
                            <li>{{ field.label }}: {{ error }}</li>
                        {% endfor %}
                    {% endfor %}
                    {% for error in form.non_field_errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}

        <form method="post" class="search-form">
            {% csrf_token %}
            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.start_date.id_for_label }}">시작 날짜</label>
                    {{ form.start_date }}
                </div>
                <div class="form-group">
                    <label for="{{ form.end_date.id_for_label }}">종료 날짜</label>
                    {{ form.end_date }}
                </div>
                <div class="form-group">
                    <label for="{{ form.kiosk.id_for_label }}">키오스크</label>
                    {{ form.kiosk }}
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        조회하기
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Redis 실시간 데이터 및 동기화 -->
    {% if redis_data %}
        <div class="redis-section">
            <h3>실시간 데이터 (오늘: {{ today|date:"Y-m-d" }})</h3>
            
            <form method="post" class="redis-sync-form">
                {% csrf_token %}
                <button type="submit" name="sync_redis" class="btn btn-secondary">
                    <i class="fas fa-sync-alt"></i>
                    Redis 데이터 동기화
                </button>
            </form>

            <div class="summary-cards">
                {% for kiosk_id, kiosk_data in redis_data.items %}
                    <div class="summary-card">
                        <h4>
                            <i class="fas fa-desktop"></i>
                            {{ kiosk_data.kiosk_name }} ({{ kiosk_data.kiosk_id }})
                        </h4>
                        <div class="summary-stats">
                            <div class="stat-item">
                                <span class="stat-number">{{ kiosk_data.counts.member_gait_count }}</span>
                                <span class="stat-label">회원 보행 분석</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">{{ kiosk_data.counts.member_body_count }}</span>
                                <span class="stat-label">회원 체형 분석</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">{{ kiosk_data.counts.guest_gait_count }}</span>
                                <span class="stat-label">비회원 보행 분석</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">{{ kiosk_data.counts.guest_body_count }}</span>
                                <span class="stat-label">비회원 체형 분석</span>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}

    <!-- 사용량 데이터 표시 -->
    {% if usage_data %}
        <!-- 요약 통계 -->
        <div class="summary-cards">
            <div class="summary-card">
                <h4>
                    <i class="fas fa-chart-bar"></i>
                    기간별 총계 ({{ usage_data.period.start|date:"Y-m-d" }} ~ {{ usage_data.period.end|date:"Y-m-d" }})
                </h4>
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-number">{{ usage_data.grand_total.member_gait_count }}</span>
                        <span class="stat-label">회원 보행 분석</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ usage_data.grand_total.member_body_count }}</span>
                        <span class="stat-label">회원 체형 분석</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ usage_data.grand_total.guest_gait_count }}</span>
                        <span class="stat-label">비회원 보행 분석</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ usage_data.grand_total.guest_body_count }}</span>
                        <span class="stat-label">비회원 체형 분석</span>
                    </div>
                </div>
            </div>

            <div class="summary-card">
                <h4>
                    <i class="fas fa-database"></i>
                    과거 데이터 (DB)
                </h4>
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-number">{{ usage_data.db_summary.total_member_gait }}</span>
                        <span class="stat-label">회원 보행 분석</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ usage_data.db_summary.total_member_body }}</span>
                        <span class="stat-label">회원 체형 분석</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ usage_data.db_summary.total_guest_gait }}</span>
                        <span class="stat-label">비회원 보행 분석</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ usage_data.db_summary.total_guest_body }}</span>
                        <span class="stat-label">비회원 체형 분석</span>
                    </div>
                </div>
            </div>

            <div class="summary-card">
                <h4>
                    <i class="fas fa-bolt"></i>
                    오늘 데이터 (Redis)
                </h4>
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-number">{{ usage_data.redis_today.member_gait_count }}</span>
                        <span class="stat-label">회원 보행 분석</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ usage_data.redis_today.member_body_count }}</span>
                        <span class="stat-label">회원 체형 분석</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ usage_data.redis_today.guest_gait_count }}</span>
                        <span class="stat-label">비회원 보행 분석</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ usage_data.redis_today.guest_body_count }}</span>
                        <span class="stat-label">비회원 체형 분석</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 일별 상세 데이터 테이블 -->
        {% if usage_data.daily_data %}
            <div class="data-table-section">
                <h3>일별 상세 데이터</h3>
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>날짜</th>
                                <th>키오스크</th>
                                <th>회원 보행 분석</th>
                                <th>회원 체형 분석</th>
                                <th>비회원 보행 분석</th>
                                <th>비회원 체형 분석</th>
                                <th>일일 총계</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for daily in usage_data.daily_data %}
                                <tr>
                                    <td>{{ daily.usage_date|date:"Y-m-d" }}</td>
                                    <td>{{ daily.kiosk__name|default:daily.kiosk__kiosk_id }}</td>
                                    <td>{{ daily.member_gait_count }}</td>
                                    <td>{{ daily.member_body_count }}</td>
                                    <td>{{ daily.guest_gait_count }}</td>
                                    <td>{{ daily.guest_body_count }}</td>
                                    <td>
                                        <strong>
                                            {% widthratio daily.member_gait_count 1 1 as mg %}
                                            {% widthratio daily.member_body_count 1 1 as mb %}
                                            {% widthratio daily.guest_gait_count 1 1 as gg %}
                                            {% widthratio daily.guest_body_count 1 1 as gb %}
                                            {{ mg|add:mb|add:gg|add:gb }}
                                        </strong>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        {% else %}
            <div class="data-table-section">
                <div class="no-data">
                    선택한 조건에 해당하는 데이터가 없습니다.
                </div>
            </div>
        {% endif %}

        {% else %}
            <div class="data-table-section">
                <div class="no-data">
                    조회 조건을 설정하고 "조회하기" 버튼을 클릭하세요.
                </div>
            </div>
    {% endif %}

    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 폼 제출시 로딩 상태 표시
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 처리중...';
            }
        });
    });

    // 날짜 필드 기본값 설정
    const today = new Date().toISOString().split('T')[0];
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    const oneMonthAgoStr = oneMonthAgo.toISOString().split('T')[0];

    const startDateField = document.querySelector('#id_start_date');
    const endDateField = document.querySelector('#id_end_date');

    if (startDateField && !startDateField.value) {
        startDateField.value = oneMonthAgoStr;
    }
    if (endDateField && !endDateField.value) {
        endDateField.value = today;
    }
});
</script>

{% endblock %}
