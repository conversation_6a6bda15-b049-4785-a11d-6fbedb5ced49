from email.policy import default

from django.db import models
from django.contrib.auth.models import AbstractUser
from django.contrib.postgres.fields import ArrayField
from django_prometheus.models import ExportModelOperationsMixin
from analysis.custom import metrics
from django.utils import timezone


class CodeInfo(models.Model):
    group_id = models.CharField(max_length=4)
    code_id = models.CharField(max_length=40)
    code_name = models.CharField(max_length=100)
    min_value = models.FloatField(null=True)
    max_value = models.FloatField(null=True)
    normal_min_value = models.FloatField(null=True)
    normal_max_value = models.FloatField(null=True)
    caution_min_value = models.FloatField(null=True)
    caution_max_value = models.FloatField(null=True)
    outline = models.CharField(max_length=1000, null=True)
    risk = models.CharField(max_length=1000, null=True, blank=True)
    improve = models.Char<PERSON>ield(max_length=1000, null=True, blank=True)
    recommended = ArrayField(models.CharField(max_length=500, null=True, blank=True), size=2, null=True, blank=True)
    title = models.CharField(max_length=100, null=True)
    title_outline = models.CharField(max_length=100, null=True)
    title_risk = models.CharField(max_length=100, null=True)
    title_improve = models.CharField(max_length=100, null=True)
    title_recommended = models.CharField(max_length=100, null=True)
    unit_name = models.CharField(max_length=20, null=True)
    measurement_type = models.CharField(max_length=100, null=True)        # 측정 항목에 대한 값 "얼굴 좌우 대칭 정도"
    seq_no = models.IntegerField(null=True)
    display_ticks = ArrayField(
        models.IntegerField(null=True, blank=True),
        size=None,
        null=True, blank=True,
        default=list,
    )
    direction = models.CharField(max_length=10, choices=[('positive', 'Positive'), ('negative', 'Negative')], null=True)
    created_dt = models.DateTimeField(auto_now_add=True)


class AuthInfo(models.Model):
    uid = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=100)
    uuid = models.CharField(max_length=100, null=True)  # For test only
    created_dt = models.DateTimeField(auto_now_add=True)


class OrganizationInfo(ExportModelOperationsMixin('organization_info'), models.Model):
    organization_name = models.CharField(max_length=100)
    contact_number = models.CharField(max_length=100, null=True)
    address = models.CharField(max_length=100, null=True)
    created_dt = models.DateTimeField(auto_now_add=True)
    code = models.CharField(max_length=50, null=True, blank=True) # NEIS - SD_SCHUL_CODE / 학교코드   
    institution_type = models.CharField(max_length=1, choices=[('S', 'School'), ('O', 'Organization')], default='O')

    def __str__(self):
        return self.organization_name


class UserInfo(ExportModelOperationsMixin('user_info'), AbstractUser):
    user_type = models.CharField(max_length=1, null=False, blank=False)
    phone_number = models.CharField(max_length=100)
    organization = models.ForeignKey(OrganizationInfo, on_delete=models.SET_NULL, null=True, blank=True)
    department = models.CharField(max_length=100, null=True, blank=True)
    student_name = models.CharField(max_length=100, null=True, blank=True) # 데이터 마이그레이션 후 삭제 예정
    user_display_name = models.CharField(max_length=100, null=True, blank=True)
    dob = models.CharField(max_length=8, null=True, blank=True)  # YYYYMMDD
    gender = models.CharField(max_length=1, null=True, blank=True)
    height = models.FloatField(null=True, blank=True)
    year = models.IntegerField(null=True, blank=True)
    created_dt = models.DateTimeField(auto_now_add=True)
    last_active_dt = models.DateTimeField(null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['year', 'department'])
        ]

    def __str__(self):
        if self.user_type in ['S', 'O']:
            return self.user_display_name
        else:
            return f'{self.phone_number}'


class KioskInfo(models.Model):
    id = models.AutoField(primary_key=True)
    kiosk_id = models.CharField(max_length=100, unique=True)  # 키오스크 ID - 중복 불가
    version = models.CharField(max_length=50, null=True, blank=True)  # 키오스크 버전 정보 기록
    location = models.CharField(max_length=100, null=True, blank=True)  # 키오스크 설치 위치 정보
    remark = models.CharField(max_length=100, null=True, blank=True)  # 비고
    active = models.BooleanField(default=True)  # 활성화 여부
    Org = models.ForeignKey(OrganizationInfo, on_delete=models.SET_NULL, null=True, blank=True)  # 관리자
    created_dt = models.DateTimeField(auto_now_add=True)  # 생성일
    last_used_dt = models.DateTimeField(null=True, blank=True)  # 마지막 사용일시

    def __str__(self):
        return f"{self.id}번 - {self.Org.organization_name if self.Org is not None else '학교/기관 없음'} - {self.kiosk_id}_{self.version + 'ver' if self.version is not None else '버전 정보 없음'}"


class SessionInfo(models.Model):
    req_type = models.CharField(max_length=1, null=True, blank=True)
    session_key = models.CharField(max_length=100)
    user_id = models.BigIntegerField(null=True)
    kiosk = models.ForeignKey(
        KioskInfo,
        on_delete=models.SET_NULL,  # KioskInfo가 삭제되면 kiosk_id를 NULL로 설정
        null=True,
        blank=True,
        to_field='kiosk_id'
    )
    is_issued = models.BooleanField(default=False)
    created_dt = models.DateTimeField(auto_now_add=True)
    last_active_dt = models.DateTimeField(auto_now_add=True, null=True, blank=True)  # 마지막 활동 시간


class FamilyUserInfo(models.Model):
    id = models.AutoField(primary_key=True)
    user = models.ForeignKey(UserInfo, on_delete=models.CASCADE, related_name='parent_user')  # 부모 사용자 ID
    family_member_name = models.CharField(max_length=100)  # 가족 구성원 이름
    relationship = models.CharField(max_length=50)  # 관계
    gender = models.CharField(max_length=1, null=True, blank=True)
    phone_number = models.CharField(max_length=100, null=True, blank=True) # Safe-R 전용 자녀 전화번호 추 후 교육청이나 기능 확장을 위해서 사용함.
    profile_image = models.BooleanField(default=False)  # 프로필 이미지 여부
    created_dt = models.DateTimeField(auto_now_add=True)
    # 해당 organization은 Safe-R 앱 간의 연동 간 자녀의 학교 정보를 저장하기 위해서 사용함. 
    organization = models.ForeignKey(OrganizationInfo, on_delete=models.SET_NULL, null=True, blank=True)  # 가족 구성원 소속 기관

    class Meta:
        indexes = [
            models.Index(fields=['user'])
        ]


class UserHist(models.Model):
    user = models.ForeignKey(UserInfo, on_delete=models.SET_NULL, null=True, blank=True)
    organization = models.ForeignKey(OrganizationInfo, on_delete=models.SET_NULL, null=True, blank=True)
    department = models.CharField(max_length=100, null=True, blank=True)
    user_display_name = models.CharField(max_length=100, null=True, blank=True)
    year = models.IntegerField(null=True, blank=True)
    created_dt = models.DateTimeField(auto_now_add=True)


class GaitResult(ExportModelOperationsMixin('gait_result'), models.Model):
    user = models.ForeignKey(UserInfo, on_delete=models.CASCADE)
    organization = models.ForeignKey(OrganizationInfo, on_delete=models.SET_NULL, null=True, blank=True)
    score = models.FloatField(null=True)
    velocity = models.FloatField(null=True)
    cadence = models.FloatField(null=True)
    cycle_time_l = models.FloatField(null=True)
    cycle_time_r = models.FloatField(null=True)
    stride_len_l = models.FloatField(null=True)
    stride_len_r = models.FloatField(null=True)
    supp_base_l = models.FloatField(null=True)
    supp_base_r = models.FloatField(null=True)
    swing_perc_l = models.FloatField(null=True)
    swing_perc_r = models.FloatField(null=True)
    stance_perc_l = models.FloatField(null=True)
    stance_perc_r = models.FloatField(null=True)
    d_supp_perc_l = models.FloatField(null=True)
    d_supp_perc_r = models.FloatField(null=True)
    toeinout_l = models.FloatField(null=True)
    toeinout_r = models.FloatField(null=True)
    stridelen_cv_l = models.FloatField(null=True)
    stridelen_cv_r = models.FloatField(null=True)
    stridetm_cv_l = models.FloatField(null=True)
    stridetm_cv_r = models.FloatField(null=True)
    created_dt = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['user', 'created_dt'])
        ]
        ordering = ['-created_dt']

    def __str__(self):
        return f"GaitResult for {self.user.username} at {self.created_dt}"

    def get_code_info(self, code_id):
        """CodeInfo 테이블에서 특정 code_id에 해당하는 normal_min_value, normal_max_value, min_value, max_value, direction을 가져옴"""
        try:
            code_info = CodeInfo.objects.get(code_id=code_id)
            return (code_info.normal_min_value, code_info.normal_max_value,
                    code_info.min_value, code_info.max_value, code_info.direction)
        except CodeInfo.DoesNotExist:
            return None, None, None, None, None

    def get_code_info(self, code_id):
        """CodeInfo 테이블에서 특정 code_id에 해당하는 normal_min_value, normal_max_value, min_value, max_value, direction을 가져옴"""
        try:
            code_info = CodeInfo.objects.get(code_id=code_id)
            return (code_info.normal_min_value, code_info.normal_max_value,
                    code_info.caution_min_value, code_info.caution_max_value,
                    code_info.min_value, code_info.max_value, code_info.direction)
        except CodeInfo.DoesNotExist:
            return None, None, None, None, None, None, None


    ### 25/05/19 Ryan 점수 계산 로직 수정 ---
    def calculate_normalized_score(self, value, code_id):
        """normal_min_value, normal_max_value, min_value, max_value, direction을 이용해 점수를 계산 (clipping 추가)"""
        normal_min, normal_max, caution_min, caution_max, min_value, max_value, direction = self.get_code_info(code_id)
        if value is None or normal_min is None or normal_max is None or caution_min is None or caution_max is None or direction is None:
            return None

        # Clipping: value가 min_value와 max_value 범위를 벗어나면 값을 제한
        if value < min_value:
            value = min_value
        elif value > max_value:
            value = max_value

        # direction에 따른 global min/max 점수 계산
        if direction == 'positive':
            # 클수록 좋은 경우: value가 min_value에 가까우면 0, max_value에 가까우면 1
            if min_value <= value <= caution_min: 
                score = 0.4 * (value - min_value) / (caution_min - min_value)
            elif caution_min <= value <= normal_min:
                score = 0.3 * (value - caution_min) / (normal_min - caution_min) + 0.4
            elif normal_min <= value <= max_value:
                score = 0.3 * (value - normal_min) / (max_value - normal_min) + 0.7
        elif direction == 'negative':
            # 작을수록 좋은 경우: value가 max_value에 가까우면 0, min_value에 가까우면 1
            if min_value <= value <= normal_max:
                score = -0.3 * (value - min_value) / (normal_max - min_value) + 1.0
            elif normal_max <= value <= caution_max:
                score = -0.3 * (value - normal_max) / (caution_max - normal_max) + 0.7
            elif caution_max <= value <= max_value:
                score = -0.4 * (value - caution_max) / (max_value - caution_max) + 0.4

        # score가 항상 0과 1 사이의 값이 되도록 보장
        return max(0, min(score, 1))

    def calculate_score(self):
        total_sum = 0
        total_weight = 0

        # 나머지 필드들에 대한 정규화 점수 계산
        fields_with_codes = [
            (self.velocity, 'velocity'),
            (self.stride_len_l, 'stride_len_l'),
            (self.stride_len_r, 'stride_len_r'),
            (self.swing_perc_l, 'swing_perc_l'),
            (self.swing_perc_r, 'swing_perc_r'),
            (self.stance_perc_l, 'stance_perc_l'),
            (self.stance_perc_r, 'stance_perc_r'),
            (self.d_supp_perc_l, 'd_supp_perc_l'),
            (self.d_supp_perc_r, 'd_supp_perc_r')
        ]

        for field, code_id in fields_with_codes:
            field_score = self.calculate_normalized_score(field, code_id)
            if field_score is not None:
                if field in ['velocity', 'stride_len_l', 'stride_len_r']:
                    total_sum += field_score * 2  # 가중치 2
                    total_weight += 2
                else:
                    total_sum += field_score
                    total_weight += 1

        # score 계산 (가중합 평균)
        if total_weight > 0:
            *_, score_max_value, _ = self.get_code_info('score')
            self.score = total_sum / total_weight * score_max_value
        else:
            self.score = None

    def save(self, *args, **kwargs):
        # score 계산 후 저장
        self.calculate_score()
        super().save(*args, **kwargs)


class BodyResult(ExportModelOperationsMixin('body_result'), models.Model):
    user = models.ForeignKey(UserInfo, on_delete=models.CASCADE)
    organization = models.ForeignKey(OrganizationInfo, on_delete=models.SET_NULL, null=True, blank=True)
    face_level_angle = models.FloatField(null=True)
    shoulder_level_angle = models.FloatField(null=True)
    hip_level_angle = models.FloatField(null=True)
    leg_length_ratio = models.FloatField(null=True)
    left_leg_alignment_angle = models.FloatField(null=True)
    right_leg_alignment_angle = models.FloatField(null=True)
    left_back_knee_angle = models.FloatField(null=True)
    right_back_knee_angle = models.FloatField(null=True)
    forward_head_angle = models.FloatField(null=True)
    scoliosis_shoulder_ratio = models.FloatField(null=True)
    scoliosis_hip_ratio = models.FloatField(null=True)
    # S3 미리 서명 URL 갱신 시 컬럼 길이 제한으로 짤려서 들어감
    image_front_url = models.CharField(max_length=500, null=True)  # 수정
    image_side_url = models.CharField(max_length=500, null=True)  # 수정
    mobile_yn = models.CharField(max_length=1, default='n')  # 체형 결과에서 키오스크와 모바일 구분하기 위함
    height = models.FloatField(null=True, blank=True, default=170.0)
    weight = models.FloatField(null=True, blank=True, default=60.0)
    # created_dt = models.DateTimeField(auto_now_add=True)
    created_dt = models.DateTimeField(default=timezone.now)
    family_user = models.ForeignKey(FamilyUserInfo, on_delete=models.CASCADE, null=True, blank=True)

    def __str__(self):
        return f"BodyResult for {self.user.username} at {self.created_dt}"

    class Meta:
        indexes = [
            models.Index(fields=['user', 'created_dt'])
        ]
        ordering = ['-created_dt']

    def save(self, *args, **kwargs):
        is_new = self.pk is None
        if is_new:
            org = self.user.organization
            if org:
                metrics.body_result_by_org.labels(
                    organization_name=org.organization_name,
                    institution_type=org.institution_type
                ).inc()
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        # 지연 임포트로 순환 참조 방지
        if hasattr(self, 'created_dt'): # Self 인스턴스에 created_dt 속성이 있는지 확인
            from .helpers import delete_s3_file # 순환 참조 방지
            
            created_dt_str = self.created_dt.strftime('%Y%m%dT%H%M%S%f')
            file_keys = [
                ('front', created_dt_str),
                ('side', created_dt_str)
            ]

            success, failed = delete_s3_file(file_keys)
            if not success:
                print(f"Error deleting files from S3: {failed}")
        
        super().delete(*args, **kwargs) # 인스턴스 삭제


### 체형 분석 결과에서 keypoints 들을 저장할 테이블
### 모바일에서만 사용함 (null = True)
class Keypoint(models.Model):
    body_result = models.ForeignKey(BodyResult, on_delete=models.CASCADE, related_name='keypoints')
    pose_type = models.CharField(max_length=5, choices=[('front', 'Front'), ('side', 'Side')])
    x = ArrayField(models.FloatField())
    y = ArrayField(models.FloatField())
    z = ArrayField(models.FloatField())
    visibility = ArrayField(models.FloatField())
    presence = ArrayField(models.FloatField())

    class Meta:
        # unique_together를 사용하여 (body_result, pose_type) 조합이 유일하도록 제한
        unique_together = ('body_result', 'pose_type')
        # 추가로 제약조건을 걸어 pose_type이 'front'나 'side'만 가능하도록 함
        constraints = [
            models.CheckConstraint(
                check=models.Q(pose_type__in=['front', 'side']),
                name='valid_pose_type'
            )
        ]


### 키오스크 사용 카운팅 테이블
### req1 = 회원 보행
### req2 = 회원 바디
### req3 = 비회원 보행
### req4 = 비회원 바디
### created_dt = 생성일(카운팅 일)
class KioskCount(models.Model):
    id = models.AutoField(primary_key=True)
    kiosk = models.ForeignKey(KioskInfo, on_delete=models.CASCADE, related_name='kiosk_info', null=True, blank=True)
    type1 = models.IntegerField(default=0)
    type2 = models.IntegerField(default=0)
    type3 = models.IntegerField(default=0)
    type4 = models.IntegerField(default=0)
    created_dt = models.DateTimeField(auto_now_add=True)

### 키오스크 일별 사용량 집계 테이블 (시계열 데이터)
### 회원 보행 : member_gait_count
### 회원 체형 : member_body_count
### 비회원 보행 : guest_gait_count
### 비회원 체형 : guest_body_count
class KioskUsageDaily(models.Model):
    """일별 키오스크 사용량 집계 테이블"""
    kiosk = models.ForeignKey(KioskInfo, on_delete=models.CASCADE, related_name='daily_usage')
    organization = models.ForeignKey(OrganizationInfo, on_delete=models.CASCADE, null=True)  # 빠른 조회용 중복 저장
    usage_date = models.DateField()  # 사용 날짜 (YYYY-MM-DD)
    
    # 사용량 카운트
    member_gait_count = models.IntegerField(default=0)    # type1: 회원 보행
    member_body_count = models.IntegerField(default=0)    # type2: 회원 체형
    guest_gait_count = models.IntegerField(default=0)     # type3: 비회원 보행
    guest_body_count = models.IntegerField(default=0)     # type4: 비회원 체형
    
    # 메타데이터
    created_dt = models.DateTimeField(auto_now_add=True)
    updated_dt = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('kiosk', 'usage_date')
        indexes = [
            models.Index(fields=['organization', 'usage_date']),  # 기관별 기간 조회용
            models.Index(fields=['usage_date']),                 # 전체 기간 조회용
            models.Index(fields=['kiosk', 'usage_date']),        # 키오스크별 기간 조회용
        ]
        db_table = 'analysis_kiosk_usage_daily'

    # 숫자를 입력 받아 해당 하는 행을 카운트 하는 메소드
    # 1 : 회원 보행
    # 2 : 회원 체형
    # 3 : 비회원 보행
    # 4 : 비회원 체형
    def increment_count(self, count_type, increment=1):
        """사용량 카운트를 증가시키는 메소드"""
        if count_type == '1':
            self.member_gait_count += increment
        elif count_type == '2':
            self.member_body_count += increment
        elif count_type == '3':
            self.guest_gait_count += increment
        elif count_type == '4':
            self.guest_body_count += increment
        else:
            raise ValueError("Invalid count type. Use 1, 2, 3, or 4.")
        self.save()
        
    def __str__(self):
        return f"{self.kiosk.kiosk_id} - {self.usage_date}"
        
    @property
    def total_count(self):
        """총 사용량"""
        return self.member_gait_count + self.member_body_count + self.guest_gait_count + self.guest_body_count
        
    @property
    def member_total(self):
        """회원 총 사용량"""
        return self.member_gait_count + self.member_body_count
        
    @property
    def guest_total(self):
        """비회원 총 사용량"""
        return self.guest_gait_count + self.guest_body_count
