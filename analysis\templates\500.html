<!DOCTYPE html>
{% load static %}
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>서버 오류</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Noto Sans KR', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
            height: 100vh;
            color: #333;
            overflow: hidden;
            position: relative;
        }
        
        .error-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 90%;
            z-index: 2;
        }
        
        .error-code {
            font-size: 6rem;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 0.5rem;
        }
        
        .error-message {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            color: #495057;
        }
        
        .error-details {
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.5;
        }
        
        .home-button {
            display: inline-block;
            background-color: #0d6efd;
            color: white;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        
        .home-button:hover {
            background-color: #0b5ed7;
        }
        
        .walking-person {
            position: absolute;
            bottom: 50px;
            left: -150px;
            width: 100px;
            height: 150px;
            z-index: 1;
            animation: walkingAnimation 20s linear infinite;
        }

        .road {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 40px;
            background-color: #adb5bd;
            z-index: 0;
        }

        .road:before {
            content: '';
            position: absolute;
            top: 50%;
            width: 100%;
            height: 4px;
            background: repeating-linear-gradient(to right, white 0px, white 30px, transparent 30px, transparent 60px);
        }

        @keyframes walkingAnimation {
            0% {
                left: -150px;
                transform: scaleX(1);
                opacity: 0;
            }
            5% {
                opacity: 1;
            }
            45% {
                opacity: 1;
            }
            49% {
                transform: scaleX(1);
                opacity: 0;
            }
            50% {
                left: 100%;
                transform: scaleX(-1);
                opacity: 0;
            }
            55% {
                opacity: 1;
            }
            95% {
                opacity: 1;
            }
            99% {
                transform: scaleX(-1);
                opacity: 0;
            }
            100% {
                left: -150px;
                transform: scaleX(1);
                opacity: 0;
            }
        }

        .person {
            width: 100%;
            height: 100%;
            position: relative;
        }

        /* 개선된 사람 스타일 */
        .head {
            position: absolute;
            top: 0;
            left: 50%;
            width: 35px;
            height: 35px;
            background-color: #555;
            border-radius: 50%;
            transform: translateX(-50%);
            z-index: 2;
        }

        .face {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 10px;
            background-color: #fff;
            border-radius: 0 0 10px 10px;
            z-index: 3;
        }

        .eye-left, .eye-right {
            position: absolute;
            top: 10px;
            width: 6px;
            height: 6px;
            background-color: #000;
            border-radius: 50%;
            z-index: 4;
        }

        .eye-left {
            left: 10px;
        }

        .eye-right {
            right: 10px;
        }

        .body {
            position: absolute;
            top: 35px;
            left: 50%;
            width: 40px;
            height: 50px;
            background-color: #4a76a8;
            border-radius: 10px 10px 0 0;
            transform: translateX(-50%);
            z-index: 1;
        }

        .arm-left, .arm-right {
            position: absolute;
            top: 40px;
            width: 10px;
            height: 40px;
            background-color: #4a76a8;
            border-radius: 5px;
            z-index: 0;
            transform-origin: top center;
        }

        .arm-left {
            left: 50%;
            transform: translateX(-30px) rotate(20deg);
            animation: swingArmLeft 0.5s infinite alternate;
        }

        .arm-right {
            right: 50%;
            transform: translateX(30px) rotate(-20deg);
            animation: swingArmRight 0.5s infinite alternate-reverse;
        }

        .hand-left, .hand-right {
            position: absolute;
            bottom: 0;
            width: 12px;
            height: 12px;
            background-color: #555;
            border-radius: 50%;
        }

        .hand-left {
            left: -1px;
        }

        .hand-right {
            right: -1px;
        }

        .leg-left, .leg-right {
            position: absolute;
            top: 85px;
            width: 15px;
            height: 50px;
            background-color: #333;
            border-radius: 5px;
            transform-origin: top center;
            z-index: 0;
        }

        .leg-left {
            left: 50%;
            transform: translateX(-18px);
            animation: swingLegLeft 0.5s infinite alternate;
        }

        .leg-right {
            right: 50%;
            transform: translateX(18px);
            animation: swingLegRight 0.5s infinite alternate-reverse;
        }

        .foot-left, .foot-right {
            position: absolute;
            bottom: -5px;
            width: 20px;
            height: 10px;
            background-color: #000;
            border-radius: 5px 10px 5px 5px;
        }

        .foot-left {
            left: -5px;
        }

        .foot-right {
            right: -5px;
            border-radius: 10px 5px 5px 5px;
        }

        .shadow {
            position: absolute;
            bottom: -45px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 12px;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 50%;
            z-index: -1;
            animation: shadowPulse 0.5s infinite alternate;
        }

        @keyframes swingArmLeft {
            0% { transform: translateX(-30px) rotate(40deg); }
            100% { transform: translateX(-30px) rotate(-20deg); }
        }

        @keyframes swingArmRight {
            0% { transform: translateX(30px) rotate(-40deg); }
            100% { transform: translateX(30px) rotate(20deg); }
        }

        @keyframes swingLegLeft {
            0% { transform: translateX(-18px) rotate(-30deg); }
            100% { transform: translateX(-18px) rotate(30deg); }
        }

        @keyframes swingLegRight {
            0% { transform: translateX(18px) rotate(30deg); }
            100% { transform: translateX(18px) rotate(-30deg); }
        }

        @keyframes shadowPulse {
            0% { width: 50px; opacity: 0.3; }
            100% { width: 60px; opacity: 0.2; }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-code">500</div>
        <div class="error-message">서버 오류가 발생했습니다</div>        <p class="error-details">
            서버에 예기치 않은 오류가 발생했습니다. <br>
            <strong>관리자에게 문의하세요.</strong><br>
            잠시 후 다시 시도해 주시거나, 문제가 계속되면 아래 버튼을 클릭하여 홈으로 돌아가세요.
        </p>
        <form method="post" action="{% url 'logout' %}" style="display: inline;">
            {% csrf_token %}
            <button type="submit" class="home-button">홈으로 돌아가기</button>
        </form>
    </div>
    
    <div class="walking-person">
        <div class="person">
            <div class="shadow"></div>
            <div class="head">
                <div class="face">
                    <div class="eye-left"></div>
                    <div class="eye-right"></div>
                </div>
            </div>
            <div class="body"></div>
            <div class="arm-left">
                <div class="hand-left"></div>
            </div>
            <div class="arm-right">
                <div class="hand-right"></div>
            </div>
            <div class="leg-left">
                <div class="foot-left"></div>
            </div>
            <div class="leg-right">
                <div class="foot-right"></div>
            </div>
        </div>
    </div>
    
    <div class="road"></div>

    <script>
        // 브라우저별 애니메이션 호환성을 위한 추가 스크립트
        document.addEventListener('DOMContentLoaded', function() {
            const person = document.querySelector('.walking-person');
            
            // 화면 크기에 따라 애니메이션 속도 조절
            function adjustAnimationSpeed() {
                const windowWidth = window.innerWidth;
                const duration = Math.max(20, windowWidth / 80); // 화면이 넓을수록 더 긴 시간 소요
                person.style.animationDuration = duration + 's';
            }
            
            // 초기 설정 및 창 크기 변경 시 조절
            adjustAnimationSpeed();
            window.addEventListener('resize', adjustAnimationSpeed);
        });
    </script>
</body>
</html>