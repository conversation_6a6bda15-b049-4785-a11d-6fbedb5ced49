# PowerShell script to restart Django application

# Load environment variables from .env file
if (Test-Path ".env") {
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^\s*([^#][^=]*)\s*=\s*(.*)\s*$") {
            $name = $matches[1].Trim()
            $value = $matches[2].Trim()
            # Remove quotes if present
            $value = $value -replace "^['""]|['""]$", ""
            [Environment]::SetEnvironmentVariable($name, $value, "Process")
            Write-Host "Set $name = $value"
        }
    }
}

# Stop Gunicorn processes (if any)
$gunicornProcesses = Get-Process -Name "gunicorn*" -ErrorAction SilentlyContinue
if ($gunicornProcesses) {
    Write-Host "Stopping Gunicorn processes:"
    $gunicornProcesses | ForEach-Object {
        Write-Host "Stopping Gunicorn process: $($_.Id)"
        Stop-Process -Id $_.Id -Force
    }
}

# Stop Python processes running mail_fetch_thread.py (if any)
$mailFetchProcesses = Get-WmiObject Win32_Process | Where-Object { $_.CommandLine -like "*mail_fetch_thread.py*" }
if ($mailFetchProcesses) {
    Write-Host "Stopping mail_fetch_thread.py processes:"
    $mailFetchProcesses | ForEach-Object {
        Write-Host "Stopping mail_fetch_thread.py process: $($_.ProcessId)"
        Stop-Process -Id $_.ProcessId -Force
    }
}

# Start mail_fetch_thread.py in background
Write-Host "Starting mail_fetch_thread.py..."
Start-Process -FilePath "python" -ArgumentList "mail_fetch_thread.py" -WindowStyle Hidden

# Start Django with Gunicorn
Write-Host "Starting Django with Gunicorn..."
Start-Process -FilePath "gunicorn" -ArgumentList "--workers=4", "--bind=0.0.0.0:44561", "--timeout=300", "--access-logfile=logs/request_log.log", "--error-logfile=logs/error_log.log", "mysite.wsgi:application" -NoNewWindow

Write-Host "Django application restarted successfully!"
