{% load static %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>웰니스 바디 스캐너</title>
    <link rel="stylesheet" href="{% static 'styles.css' %}">
    <link rel="shortcut icon" type="image/png" href="{% static 'images/favicon.ico' %}" >
    <link href="{% static 'fontawesomefree/css/fontawesome.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'fontawesomefree/css/brands.css' %}" rel="stylesheet" type="text/css">
    <link href="{% static 'fontawesomefree/css/solid.css' %}" rel="stylesheet" type="text/css">
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-2RNGQ9YFFS"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-2RNGQ9YFFS');
    </script>
    <style>
        /* 플로팅 메시지 스타일 */
        .floating-alert {
            position: fixed;
            top: -100px; /* 초기에는 화면 밖에 위치 */
            left: 50%;
            transform: translateX(-50%);
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            opacity: 0;
            transition: top 0.5s ease, opacity 0.5s ease;
            display: flex;
            align-items: center;
            max-width: 90%;
            text-align: center;
        }

        .floating-alert.show {
            top: 20px;
            opacity: 1;
        }

        /* 다중 알림을 위한 스타일 추가 */
        .floating-alert.second {
            top: 90px; /* 첫 번째 알림 아래에 위치 */
        }

        .floating-alert.third {
            top: 160px; /* 두 번째 알림 아래에 위치 */
        }

        .floating-alert i {
            margin-right: 10px;
            color: #721c24;
        }

        /* 닫기 버튼 스타일 */
        .close-alert {
            margin-left: 15px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            color: #721c24;
        }
    </style>
</head>
<body>
    <!-- 플로팅 알림 메시지 템플릿 -->
    <div id="alertTemplate" style="display: none;">
        <div class="floating-alert">
            <i class="fas fa-exclamation-circle"></i>
            <span class="alert-message"></span>
            <span class="close-alert">&times;</span>
        </div>
    </div>

    <script>
        function openPasswordChangeWindow() {
            window.open("{% url 'password_change' %}", "PasswordChangeWindow", "width=600,height=600");
        }
        
        // 알림 ID 카운터
        let alertCounter = 0;
        // 활성화된 알림 추적
        let activeAlerts = [];
        
        // 단일 알림 표시 함수
        function showAlert(message) {
            // 템플릿에서 새 알림 요소 생성
            const template = document.getElementById('alertTemplate');
            const newAlert = template.querySelector('.floating-alert').cloneNode(true);
            
            // 고유 ID 할당
            const alertId = 'alert-' + alertCounter++;
            newAlert.id = alertId;
            
            // 메시지 설정
            newAlert.querySelector('.alert-message').textContent = message;
            
            // 닫기 버튼에 이벤트 연결
            newAlert.querySelector('.close-alert').addEventListener('click', function() {
                closeSpecificAlert(alertId);
            });
            
            // 알림 위치 조정
            const alertPosition = activeAlerts.length;
            if (alertPosition === 1) {
                newAlert.classList.add('second');
            } else if (alertPosition === 2) {
                newAlert.classList.add('third');
            } else if (alertPosition > 2) {
                // 너무 많은 알림이 있으면 가장 오래된 것을 제거
                closeSpecificAlert(activeAlerts[0]);
                newAlert.classList.add('third');
            }
            
            // 알림 표시
            document.body.appendChild(newAlert);
            
            // 활성 알림 목록에 추가
            activeAlerts.push(alertId);
            
            // 애니메이션을 위한 딜레이
            setTimeout(function() {
                newAlert.classList.add('show');
            }, 10);
            
            // 자동 닫기 타이머 설정
            setTimeout(function() {
                if (document.getElementById(alertId)) {
                    closeSpecificAlert(alertId);
                }
            }, 5000);
            
            return alertId;
        }
        
        // 특정 알림 닫기
        function closeSpecificAlert(alertId) {
            const alertElement = document.getElementById(alertId);
            if (alertElement) {
                // 애니메이션 클래스 제거
                alertElement.classList.remove('show');
                
                // 활성 알림 목록에서 제거
                const index = activeAlerts.indexOf(alertId);
                if (index > -1) {
                    activeAlerts.splice(index, 1);
                }
                
                // 닫힘 애니메이션 후 요소 제거
                setTimeout(function() {
                    if (alertElement.parentNode) {
                        alertElement.parentNode.removeChild(alertElement);
                    }
                    
                    // 남아있는 알림들의 위치 재조정
                    repositionAlerts();
                }, 500);
            }
        }
        
        // 알림 위치 재조정
        function repositionAlerts() {
            for (let i = 0; i < activeAlerts.length; i++) {
                const alert = document.getElementById(activeAlerts[i]);
                if (alert) {
                    // 모든 위치 클래스 제거
                    alert.classList.remove('second', 'third');
                    
                    // 적절한 위치 클래스 추가
                    if (i === 1) {
                        alert.classList.add('second');
                    } else if (i === 2) {
                        alert.classList.add('third');
                    }
                }
            }
        }
        
        // 전역 함수로 노출 (자식 템플릿에서 직접 호출 가능)
        window.showGlobalAlert = function(message) {
            return showAlert(message);
        };
        
        // 이전 버전과의 호환성을 위한 함수
        function closeAlert() {
            // 가장 최근 알림 닫기
            if (activeAlerts.length > 0) {
                closeSpecificAlert(activeAlerts[activeAlerts.length - 1]);
            }
        }
    </script>

    <div class="container">
        <div class="sidebar">
            <h2>웰니스 바디 스캐너</h2>
            <ul>
                <li><a href="{% url 'main' %}"><i class="fas fa-home menu-icon"></i>&nbsp 홈</a></li>
                <li><a href="{% url 'member_register' %}"><i class="fas fa-user-plus menu-icon"></i>&nbsp 사용자 정보 등록</a></li>
                {% comment %} <li><a href="{% url 'report' %}"><i class="fas fa-chart-bar menu-icon"></i>&nbsp체형 검사 결과 조회</a></li> {% endcomment %}
                <li><a href="{% url 'report' %}"><i class="fas fa-person menu-icon"></i>&nbsp체형 검사 결과 조회</a></li>
                <li><a href="{% url 'gait_report' %}"><i class="fas fa-person-walking menu-icon" style="padding-right: 3px; padding-left: 3px;"></i>&nbsp보행 검사 결과 조회</a></li>
                <li><a href="{% url 'policy' %}"><i class="fas fa-shield-alt menu-icon"></i>&nbsp 개인정보 처리 방침</a></li>
                <li><a href="#" onclick="openPasswordChangeWindow()"><i class="fas fa-key menu-icon"></i>&nbsp 비밀번호 변경</a></li>
                <li><a href="{% url 'management' %}"><i class="fas fa-users-gear menu-icon"></i>&nbsp 회원 관리</a></li>
            </ul>            
            <div class="user-info">
                {% if user.is_authenticated %}
                    <p style="font-size: large;">
                        <i class="fas fa-user menu-icon fa-lg"></i>&nbsp 안녕하세요, {{ user.username }}님!
                    </p>
                    <form method="post" action="{% url 'logout' %}">
                        {% csrf_token %}
                        <button type="submit" class="logout-button">로그아웃</button>
                    </form>
                {% else %}
                    <a href="{% url 'login' %}">로그인</a>
                {% endif %}
            </div>
        </div>
        <div class="content">
            {% block content %}
            {% endblock %}
        </div>
    </div>
</body>
</html>
