{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}기관 회원 관리{% endblock %}

{% block content %}

<!-- AG Grid 라이브러리 로드 -->
<script src="https://cdn.jsdelivr.net/npm/ag-grid-community/dist/ag-grid-community.min.js"></script>

<style>
    .dashboard-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
        padding-bottom: 1rem;
        display: flex;
        flex-direction: column;
    }
    
    .header-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        border-bottom: 1px solid #e1e4e8;
        padding-bottom: 1rem;
    }
    
    .org-info {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .org-info i {
        font-size: 2.5rem;
        color: #111;
    }
    
    .org-info h2 {
        font-size: 1.8rem;
        margin: 0;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
    }
    
    .primary-btn {
        padding: 0.6rem 1.2rem;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
        font-weight: 500;
    }
    
    .primary-btn:hover {
        background-color: #45a049;
    }
    
    .secondary-btn {
        padding: 0.6rem 1.2rem;
        background-color: #f8f9fa;
        color: #333;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        font-weight: 500;
    }
    
    .secondary-btn:hover {
        background-color: #e9ecef;
    }
    
    .grid-container {
        width: 100%;
        height: 600px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .filters-section {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .search-container {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }
    
    .search-input {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        width: 250px;
    }
    
    .filter-container {
        display: flex;
        gap: 1rem;
        align-items: center;
    }
    
    .filter-select {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
        border-radius: 4px;
    }
    
    .status-tag {
        padding: 0.2rem 0.5rem;
        border-radius: 12px;
        font-size: 0.8rem;
    }
    
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        justify-content: center;
        align-items: center;
    }
    
    .modal-content {
        background-color: white;
        padding: 2rem;
        border-radius: 8px;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
    
    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .modal-title {
        font-size: 1.5rem;
        margin: 0;
    }
    
    .close-button {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #aaa;
    }
    
    .close-button:hover {
        color: #333;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }
    
    .form-control {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #dee2e6;
        border-radius: 4px;
    }
    
    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 1.5rem;
    }
    
    .alert {
        padding: 1rem;
        border-radius: 4px;
        margin-bottom: 1.5rem;
        display: none;
    }
    
    .alert-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    
    .alert-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    
    .spinner-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 2000;
    }
    
    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #4CAF50;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .stats-container {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
        flex-wrap: wrap;
    }
    
    .stat-card {
        flex: 1;
        min-width: 200px;
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        text-align: center;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #4CAF50;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #666;
        font-size: 0.9rem;
    }
    
    @media (max-width: 768px) {
        .header-section {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .action-buttons {
            width: 100%;
            justify-content: center;
        }
        
        .filters-section {
            flex-direction: column;
        }
        
        .search-container, .filter-container {
            width: 100%;
        }
        
        .grid-container {
            height: 400px;
        }
        
        .stat-card {
            min-width: 100%;
        }
    }
</style>

<div class="dashboard-container">
    <!-- 알림 메시지 -->
    <div class="alert alert-success" id="successAlert"></div>
    <div class="alert alert-error" id="errorAlert"></div>
    
    <!-- 헤더 섹션 -->
    <div class="header-section">
        <div class="org-info">
            {% if user_type == 'S' %}
            <i class="fas fa-school"></i>
            {% else %}
            <i class="fas fa-building"></i>
            {% endif %}
            <h2>회원 관리</h2>
        </div>        <div class="action-buttons">
            <button id="exportBtn" class="secondary-btn">
                <i class="fas fa-download"></i> 내보내기
            </button>
        </div>
    </div>
    
    <!-- 통계 섹션 -->
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-number" id="totalMembers">0</div>
            <div class="stat-label">전체 회원수</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="activeMembers">0</div>
            <div class="stat-label">활성 회원</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="inactiveMembers">0</div>
            <div class="stat-label">비활성 회원</div>
        </div>
    </div>    <div class="description" style="flex: 1;
    min-width: 200px;
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center; color: #666;
    margin-bottom: 1.5rem;">
        <h3><i class="fas fa-users"></i> 회원 관리 시스템</h3>
        <div style="text-align: left; margin-top: 15px;">
            <h4><i class="fas fa-search"></i> 회원 검색 및 필터링</h4>
            <p>상단의 검색창과 필터를 활용하여 이름, 전화번호, 부서 등으로 회원을 빠르게 찾을 수 있습니다. 상태별(활성/비활성) 또는 부서별 필터링도 가능합니다.</p>
            <h4><i class="fas fa-edit"></i> 회원 정보 수정</h4>
            <p>테이블에서 직접 셀을 클릭하여 회원 정보를 편집할 수 있습니다. 수정된 정보는 자동으로 저장됩니다.</p>
            <h4><i class="fas fa-trash"></i> 회원 삭제</h4>
            <p>삭제 버튼을 클릭하여 더 이상 필요하지 않은 회원 정보를 삭제할 수 있습니다. 삭제 전 확인 과정이 있어 실수를 방지합니다.</p>
            <h4><i class="fas fa-download"></i> 데이터 내보내기</h4>
            <p>상단의 '내보내기' 버튼을 클릭하여 현재 표시된 회원 목록을 CSV 파일로 저장할 수 있습니다.</p>
        </div>
    </div>
    
    <!-- 필터 섹션 -->
    <div class="filters-section">
        <div class="search-container">
            <input type="text" id="searchInput" class="search-input" placeholder="이름, 전화번호 등 검색">
            <button id="searchBtn" class="secondary-btn">검색</button>
        </div>
        <div class="filter-container">
            <button id="refreshBtn" class="secondary-btn">
                <i class="fas fa-sync-alt"></i> 새로고침
                </button>
            <select id="statusFilter" class="filter-select">
                <option value="all">전체 상태</option>
                <option value="active">활성</option>
                <option value="inactive">비활성</option>
            </select>
            <select id="departmentFilter" class="filter-select">
                <option value="all">전체 부서</option>
                <!-- 부서 옵션은 자바스크립트로 생성 -->
            </select>
        </div>
    </div>
    
    <!-- AG Grid 컨테이너 -->
    <div id="memberGrid" class="ag-theme-alpine grid-container"></div>
    
    <!-- 회원 추가/수정 모달 -->
    {% comment %} <div id="memberModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">회원 추가</h3>
                <button class="close-button" id="closeModal">&times;</button>
            </div>
            <form id="memberForm">
                <div class="form-group">
                    <label for="memberName">이름</label>
                    <input type="text" id="memberName" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="memberPhone">전화번호</label>
                    <input type="tel" id="memberPhone" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="memberBirth">생년월일 (4자리)</label>
                    <input type="number" id="memberBirth" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="memberGender">성별</label>
                    <select id="memberGender" class="form-control" required>
                        <option value="M">남성</option>
                        <option value="F">여성</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="memberDepartment">부서명</label>
                    <input type="text" id="memberDepartment" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="memberStatus">회원 상태</label>
                    <select id="memberStatus" class="form-control" required>
                        <option value="active">활성</option>
                        <option value="inactive">비활성</option>
                    </select>
                </div>
                <div class="modal-footer">
                    <button type="button" class="secondary-btn" id="cancelBtn">취소</button>
                    <button type="submit" class="primary-btn" id="saveBtn">저장</button>
                </div>
            </form>
        </div>
    </div> {% endcomment %}
    
    <!-- 로딩 스피너 -->
    <div id="spinnerContainer" class="spinner-container">
        <div class="spinner"></div>
    </div>

    <!-- 회원 삭제 확인 모달 -->
    <div id="deleteConfirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">회원 삭제 확인</h3>
                <button class="close-button" id="closeDeleteModal">&times;</button>
            </div>
            <p>정말로 이 회원을 삭제하시겠습니까?</p>
            <p>이 작업은 되돌릴 수 없습니다.</p>
            <div class="modal-footer">
                <button type="button" class="secondary-btn" id="cancelDeleteBtn">취소</button>
                <button type="button" class="primary-btn" id="confirmDeleteBtn" style="background-color: #dc3545;">삭제</button>
            </div>
        </div>
    </div>
</div>

<script src="/static/js/ag-grid_locale.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {    // DOM 요소
    const memberGrid = document.getElementById('memberGrid');
    const exportBtn = document.getElementById('exportBtn');
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    const statusFilter = document.getElementById('statusFilter');
    const departmentFilter = document.getElementById('departmentFilter');
    const successAlert = document.getElementById('successAlert');
    const errorAlert = document.getElementById('errorAlert');
    const spinnerContainer = document.getElementById('spinnerContainer');
    const deleteConfirmModal = document.getElementById('deleteConfirmModal');
    const closeDeleteModal = document.getElementById('closeDeleteModal');
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const refreshBtn = document.getElementById('refreshBtn');
    let deletingMemberId = null; // 삭제할 회원 ID 변수 추가

    // CSRF 토큰
    const csrfToken = "{{ csrf_token }}";
    
    const users = JSON.parse(JSON.stringify({{ users|safe }}));
    // console.log(users);
    
    // 상태 변수
    let gridApi = null;
    let gridColumnApi = null;
    let membersData = [];
    let originalMembersData = [];
    let isEditMode = false;
    let editingMemberPhoneNumber = null; // 수정할 사용자의 PK (pnb)
    let deletingMemberPhoneNumber = null; // 삭제할 사용자의 PK (pnb) 
      // AG Grid 초기화
    function initializeGrid(data) {
        // AG Grid 컬럼 정의
        const columnDefs = [
            {
                headerName: 'ID',
                field: 'id',
                hide: true
            },            {
                headerName: '이름',
                field: 'user_display_name',
                filter: 'agTextColumnFilter',
                sortable: true,
                resizable: true,
                editable: true,
                cellEditor: 'agTextCellEditor'
            },
            {
                headerName: '전화번호',
                field: 'phone_number',
                filter: 'agTextColumnFilter',
                sortable: true,
                resizable: true,
                editable: true,
                cellEditor: 'agTextCellEditor'
            },
            {% if user_type == 'S' %}            
            {
                headerName: '부서/학급',
                field: 'department',
                filter: 'agTextColumnFilter',
                sortable: true,
                resizable: true,
                editable: true,
                cellEditor: 'agTextCellEditor'
            },            
            {% else %}
            {
                headerName: '부서',
                field: 'department',
                filter: 'agTextColumnFilter',
                sortable: true,
                resizable: true,
                editable: true,
                cellEditor: 'agTextCellEditor'
            },
            {% endif %}            
            {
                headerName: '생년월일',
                field: 'dob',
                filter: 'agTextColumnFilter',
                sortable: true,
                resizable: true,
                editable: true,
                cellEditor: 'agTextCellEditor'
            },
            {
                headerName: '성별',
                field: 'gender',
                filter: 'agTextColumnFilter',
                sortable: true,
                resizable: true,
                editable: true,
                cellEditor: 'agSelectCellEditor', 
                cellEditorParams: {
                    values: ['M', 'F']
                },
                valueFormatter: params => params.value === 'M' ? '남성' : '여성'
            },            
            {
                headerName: '상태',
                field: 'is_active',
                filter: 'agTextColumnFilter',
                sortable: true,
                resizable: true,
                editable: true,
                cellEditor: 'agSelectCellEditor',
                cellEditorParams: {
                    values: ['active', 'inactive']
                },
                cellRenderer: params => {
                    const status = params.value;
                    if (status === 'active') {
                        return `<span class="status-tag status-active">활성</span>`;
                    } else {
                        return `<span class="status-tag status-inactive">비활성</span>`;
                    }
                }
            },
            {
                headerName: '마지막 방문',
                field: 'last_login',
                filter: 'agDateColumnFilter',
                sortable: true,
                resizable: true
            },            {
                headerName: '작업',
                field: 'actions',
                colId: 'actions',
                pinned: 'right',
                resizable: false,
                sortable: false,
                filter: false,
                cellRenderer: params => {
                    return `
                        <button class="delete-btn" data-id="${params.data.id}" style="background: none; border: none; cursor: pointer; color: #dc3545; margin-left: 10px;">
                            <i class="fas fa-trash"></i>
                        </button>
                    `;
                },
                cellStyle: {
                    textAlign: 'center'
                },
                width: 100
            }
        ];        // AG Grid 옵션
        const gridOptions = {
            columnDefs: columnDefs,
            rowData: data,
            pagination: true,
            paginationPageSize: 20,
            rowSelection: 'single',
            animateRows: true,
            localeText: AG_GRID_LOCALE_KR,
            defaultColDef: {
                flex: 1,
                minWidth: 100,
                filter: true,
                sortable: true,
                resizable: true,
                suppressHeaderMenuButton: true,
                floatingFilter: true
            },
            // 선택 기능 옵션 추가
            rowMultiSelectWithClick: false,
            suppressRowClickSelection: false,
            // 체크박스 설정 추가
            checkboxSelection: true,
            headerCheckboxSelection: true,
            // 셀 값 변경 이벤트 처리
            onCellValueChanged: params => {
                // 변경된 셀 데이터 처리
                const field = params.column.colId;
                const newValue = params.newValue;
                const data = params.data;
                
                // console.log(`셀 값 변경: ${field} = ${newValue}, ID = ${data.id}, PhoneNumber = ${data.phone_number}`);
                
                // 회원 정보 업데이트를 위한 객체 생성
                const updateData = {
                    id: data.id,
                    phone_number: data.phone_number
                };
                  // 필드 값 직접 설정 (이미 백엔드 모델 필드명과 동일함)
                updateData[field] = newValue;
                
                // is_active 필드의 경우 문자열을 불리언으로 변환
                if (field === 'is_active') {
                    updateData.is_active = newValue === 'active';
                }
                
                // 필드가 잘못된 경우 로그만 출력
                if (!['user_display_name', 'phone_number', 'department', 'dob', 'gender', 'is_active'].includes(field)) {
                    // console.log(`Field ${field} may not be valid for the backend API.`);
                }
                
                // 변경 사항이 있을 경우에만 API 호출
                if (Object.keys(updateData).length > 1) {
                    updateCellData([updateData]);
                }
            },
            onGridReady: params => {
                gridApi = params.api;
                gridColumnApi = params.columnApi;
                gridApi.sizeColumnsToFit();
                
                // 이벤트 리스너 설정
                window.addEventListener('resize', () => {
                    setTimeout(() => {
                        gridApi.sizeColumnsToFit();
                    }, 100);
                });
                
                // 셀 클릭 이벤트 등록
                gridApi.addEventListener('cellClicked', onCellClicked);
            }
        };
        
        // AG Grid 생성
        new agGrid.createGrid(memberGrid, gridOptions);
        
        // AG Grid 버전 호환성을 위한 초기 설정
        if (typeof agGrid.GridOptionsWrapper?.prototype?.generateRowIdFromData === 'function') {
            // Modern AG Grid
            // console.log('Modern AG Grid detected');
        } else {
            // Legacy AG Grid
            // console.log('Legacy AG Grid detected - 일부 기능이 제한될 수 있습니다');
        }
        
        return gridOptions;
    }
    
    // 셀 클릭 이벤트 핸들러    
    function onCellClicked(params) {
        // 작업 열의 버튼 클릭 처리
        // console.log('Cell clicked:', params.column.colId);
        if (params.column.colId === 'actions') {
            const target = params.event.target;
            const editBtn = target.closest('.edit-btn');
            const deleteBtn = target.closest('.delete-btn');
            
            // console.log('Target:', target);
            // console.log('Edit button:', editBtn);
            // console.log('Delete button:', deleteBtn);
            
            if (editBtn) {
                const id = parseInt(editBtn.getAttribute('data-id'));
                // console.log('Edit member with ID:', id);
                editMember(id);
            } else if (deleteBtn) {
                const id = parseInt(deleteBtn.getAttribute('data-id'));
                // console.log('Show delete confirmation for ID:', id);
                showDeleteConfirmation(id);
            }
        }
    }
      // 회원 데이터 로드
    function loadMemberData() {
        showSpinner();
        
        // 검색 및 필터 파라미터 구성
        const searchTerm = searchInput.value;
        const status = statusFilter.value;
        let params = new URLSearchParams();
        
        if (searchTerm) params.append('search', searchTerm);
        if (status !== 'all') params.append('status', status);
        const department = departmentFilter.value;
        if (department !== 'all') params.append('department', department);        
        // AG Grid에 표시할 데이터 구성
        // 초기 데이터는 users에서 가져오기
        let transformedData = users.map(user => {
            return {
                id: user.id,
                user_display_name: user.user_display_name,
                phone_number: user.phone_number,
                department: user.department,
                dob: user.dob,
                gender: user.gender,
                is_active: user.is_active ? 'active' : 'inactive',
                last_login: user.last_login ? new Date(user.last_login).toLocaleDateString() : '-'
            };
        });

        originalMembersData = transformedData;       
          // 검색 필터 적용
        if (searchTerm) {
            transformedData = transformedData.filter(user => {
                return user.user_display_name?.includes(searchTerm) || 
                       user.phone_number?.includes(searchTerm) ||
                       user.department?.includes(searchTerm);
            });
        }
        
        // 상태 필터 적용
        if (status !== 'all') {
            transformedData = transformedData.filter(user => user.is_active === status);
        }
        
        // 부서 필터 적용
        if (department !== 'all') {
            transformedData = transformedData.filter(user => user.department === department);
        }
        membersData = transformedData;

        // AG Grid 초기화 또는 업데이트
        if (gridApi) {
            // AG Grid v28+ 이상에서는 setRowData 대신 직접 데이터 참조 관리
            gridApi.setGridOption('rowData', membersData);
        } else {
            const gridOptions = initializeGrid(membersData);
            gridApi = gridOptions.api;
        }
        // 통계 업데이트
        updateStats({
            total: transformedData.length,
            active: transformedData.filter(user => user.is_active === 'active').length,
            inactive: transformedData.filter(user => user.is_active === 'inactive').length
        });
        
        // 필터 옵션 업데이트
        updateFilterOptions({
            departments: [...new Set(transformedData.map(user => user.department).filter(Boolean))].sort(),
        }, department);
        
        hideSpinner();
    }
    
    // 통계 업데이트
    function updateStats(stats) {
        document.getElementById('totalMembers').textContent = stats.total;
        document.getElementById('activeMembers').textContent = stats.active;
        document.getElementById('inactiveMembers').textContent = stats.inactive;
    }
    
    // 필터 옵션 업데이트
    function updateFilterOptions(filterOptions, selectedDepartment) {
        // 부서 필터 옵션 업데이트
        const originalDepts = [...new Set(originalMembersData.map(user => user.department))];

        // console.log('Original departments:', originalDepts);
        if (filterOptions.departments && filterOptions.departments.length > 0) {
            const departmentOptions = originalDepts.map(dept => {
                return `<option value="${dept}" ${dept === selectedDepartment ? 'selected' : ''}>${dept}</option>`;
            }
            ).join('');
            departmentFilter.innerHTML = '<option value="all">전체 부서</option>' + departmentOptions;
        }
    }    // 회원 추가 기능은 제거되었습니다
    
    // 회원 삭제 확인 모달 표시    
    function showDeleteConfirmation(id) {
        // console.log('Setting deletingMemberId to:', id);
        deletingMemberId = id;
        deleteConfirmModal.style.display = 'flex';
        // console.log('Delete confirmation modal displayed');
    }
    
    // 회원 삭제 확인 모달 닫기
    function closeDeleteConfirmModal() {
        deleteConfirmModal.style.display = 'none';
    }    // 회원 수정 - 수정 기능은 인라인 셀 편집으로만 가능하도록 변경
    function editMember(id) {
        const member = membersData.find(m => m.id === id);
        if (!member) {
            showErrorAlert('회원 정보를 찾을 수 없습니다.');
            return;
        }
        
        // 수정 기능은 인라인 셀 편집으로 처리됨
        showSuccessAlert('회원 정보는 테이블에서 직접 셀을 클릭하여 수정할 수 있습니다.');
    }// 회원 삭제    
    function deleteMember(id) {
        if (!id) {
            showErrorAlert('회원 ID가 필요합니다.');
            hideSpinner();
            closeDeleteConfirmModal();
            return;
        }
        
        showSpinner();
        
        // 삭제할 회원 정보 가져오기
        const member = membersData.find(m => m.id === id);
        if (!member) {
            showErrorAlert('회원 정보를 찾을 수 없습니다.');
            hideSpinner();
            closeDeleteConfirmModal();
            return;
        }
        
        // CSRF 토큰 가져오기 (추가)
        // console.log('Deleting member with ID:', id);
        
        // 백엔드 API는 id를 URL에서 받고 별도의 body가 필요 없음
        fetch(`/manage/delete/${id}/`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            }
            // body는 필요 없음 - URL에서 id를 받아 처리
        })        .then(response => {
            // console.log('Response status:', response.status);
            return response.json().then(data => {
                // console.log('Response data:', data);
                if (!response.ok) {
                    throw new Error(data.error || '회원 삭제에 실패했습니다.');
                }
                return data;
            }).catch(err => {
                // JSON 파싱 실패 시
                console.error('JSON parsing error:', err);
                if (!response.ok) {
                    throw new Error('회원 삭제에 실패했습니다. 상태 코드: ' + response.status);
                }
                return { message: '회원이 성공적으로 삭제되었습니다.' };
            });
        })        .then(data => {
            // console.log('Delete successful, updating UI');
            // 클라이언트 측에서 삭제된 회원을 배열에서 제거
            membersData = membersData.filter(m => m.id !== id);
            
            // 그리드 업데이트
            if (gridApi) {
                // console.log('Updating grid with filtered data, remaining members:', membersData.length);
                if (gridApi) {
                // AG Grid v28+ 이상에서는 setRowData 대신 직접 데이터 참조 관리
                gridApi.setGridOption('rowData', membersData);
                } else {
                    const gridOptions = initializeGrid(membersData);
                    gridApi = gridOptions.api;
                }
            } else {
                console.error('Grid API not available');
            }
            
            // 통계 업데이트
            updateStats({
                total: membersData.length,
                active: membersData.filter(user => user.is_active === true).length,
                inactive: membersData.filter(user => user.is_active === false).length
            });
            
            showSuccessAlert(data.message || '회원이 성공적으로 삭제되었습니다.');
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorAlert(error.message);
        })
        .finally(() => {
            hideSpinner();
            closeDeleteConfirmModal();
        });
    }      

    // 데이터 내보내기
    function exportData() {
        if (gridApi) {
            gridApi.exportDataAsCsv({
                fileName: '회원목록_' + new Date().toISOString().split('T')[0] + '.csv',
                skipHeader: false,
                skipFooters: true,
                skipGroups: true,
                allColumns: true,
                onlySelected: false,
                columnSeparator: ',',
                suppressQuotes: false
            });
        }
    }

    // 새로고침 버튼 
    refreshBtn.addEventListener('click', () => {
        searchInput.value = '';
        statusFilter.value = 'all';
        departmentFilter.value = 'all';
        // loadMemberData();
        
        window.location.reload();
    });

// 성공 알림 표시
    function showSuccessAlert(message) {
        successAlert.textContent = message;
        successAlert.style.display = 'block';
        
        // 자연스러운 알림 애니메이션
        successAlert.style.opacity = '0';
        successAlert.style.transition = 'opacity 0.3s ease-in-out';
        
        setTimeout(() => {
            successAlert.style.opacity = '1';
        }, 10);
        
        setTimeout(() => {
            successAlert.style.opacity = '0';
            setTimeout(() => {
                successAlert.style.display = 'none';
            }, 300);
        }, 3000);
    }
    
    // 오류 알림 표시
    function showErrorAlert(message) {
        errorAlert.textContent = message;
        errorAlert.style.display = 'block';
        
        // 자연스러운 알림 애니메이션
        errorAlert.style.opacity = '0';
        errorAlert.style.transition = 'opacity 0.3s ease-in-out';
        
        setTimeout(() => {
            errorAlert.style.opacity = '1';
        }, 10);
        
        setTimeout(() => {
            errorAlert.style.opacity = '0';
            setTimeout(() => {
                errorAlert.style.display = 'none';
            }, 300);
        }, 5000);  // 오류는 더 오래 표시
    }
    
    // 스피너 표시
    function showSpinner() {
        spinnerContainer.style.display = 'flex';
    }
    
    // 스피너 숨기기
    function hideSpinner() {
        spinnerContainer.style.display = 'none';
    }
    
    // 셀 직접 수정 시 데이터 업데이트
    function updateCellData(updateData) {
        showSpinner();
        
        fetch('/manage/update/', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(updateData)
        })
        .then(response => {
            return response.json().then(data => {
                if (!response.ok) {
                    throw new Error(data.error || '회원 수정에 실패했습니다.');
                }
                return data;
            });
        })
        .then(data => {
            // 성공 메시지 표시
            showSuccessAlert('회원 정보가 성공적으로 업데이트되었습니다.');
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorAlert(error.message);
            
            // 오류 발생 시 데이터 새로고침
            loadMemberData();
        })
        .finally(() => {
            hideSpinner();
        });
    }
      // 이벤트 리스너
    exportBtn.addEventListener('click', exportData);
    searchBtn.addEventListener('click', loadMemberData);
    searchInput.addEventListener('keyup', e => {
        if (e.key === 'Enter') loadMemberData();
    });
    statusFilter.addEventListener('change', loadMemberData);
    departmentFilter.addEventListener('change', loadMemberData);
          closeDeleteModal.addEventListener('click', closeDeleteConfirmModal);
    cancelDeleteBtn.addEventListener('click', closeDeleteConfirmModal);
    
    // 삭제 확인 버튼에 이벤트 리스너 추가 (개선)
    confirmDeleteBtn.addEventListener('click', () => {
        // console.log('Delete confirmation button clicked');
        if (deletingMemberId) {
            // console.log('Calling deleteMember with ID:', deletingMemberId);
            deleteMember(deletingMemberId);
        } else {
            console.error('No member ID to delete');
            showErrorAlert('삭제할 회원 ID가 없습니다.');
        }
    });    
    // 초기 데이터 로드
    loadMemberData();
});
</script>
{% endblock %}