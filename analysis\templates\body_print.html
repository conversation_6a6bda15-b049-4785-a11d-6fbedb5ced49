{% load static %}
{% load custom_filters %}

<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=0.5">
    <title>체형 분석 보고서 - 인쇄용</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 0 auto;
            /* padding: 10px; */
        }

        .top_left_container { /* AICU 심볼 및 바디스캐너 Div */
            margin-top: 10px;
            display: flex;
            align-items: center;
        }

        .top_left_container > h2 { /* 최상단 바디스캐너 텍스트  */
            margin-left: 10px;
            font-size: 20px;
            font-weight: 600;
            color: #828282;
        }

        .top_left_container > img { /* AICU 심볼 이미지 */
            width: 84px;
            height: 24px;
            object-fit: contain;
        }

        .top_left_container > #top-text {
            font-size: 25px;
            font-weight: 600;
            margin: -5px 0 0 10px;
        }

        .userinfo {
            margin-top: 20px;
            border-bottom: 3px solid #000;
            border-top: 3px solid #000;
            padding: 4px;
            display: table;

        }

        .userinfo div {
            display: table-row;
            text-align: left;
        }

        .userinfo div > div {
            display: table-cell;
            padding: 5px 10px;

        }

        .userinfo .header {
            font-weight: bold;
            border-bottom: 2px solid #000;

        }

        .userinfo .date {
            text-align: left;
        }

        #top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
        }

        .sub_title { /* 정면/측면 측정결과 text */
            margin: 0;
            padding: 1px;
            display: inline-block;
            background-color: black;
            color: white;
        }


        /* ################################ */
        /*         막대그래프 스타일          */
        /* ################################ */

        .left_inside_container {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-start;
            gap: 10px;
        }


        #sliders-container {
            margin-left: 2mm;
            width: 40mm;
        }

        #front-sliders-container, #side-sliders-container {
            flex: 1;
            max-width: 50%;
            margin-left: 10px;
        }

        #frontCanvas, #sideCanvas {
            flex: 1;
            max-width: 50%;
        }

        .range-container {
            margin: 10px 0;
        }

        /* 제목과 현재 값 */
        .range-container p {
            font-size: 12px;
            margin-bottom: 5px;
            text-align: left;
        }

        /* 슬라이더 트랙 */
        .slider-wrapper {
            position: relative;
            height: 10px;
            background-color: #ddd;
            border-radius: 5px;
        }

        .track {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 5px;
        }

        /* 현재 값을 나타내는 인디케이터 */
        .indicator {
            position: absolute;
            height: 20px;
            width: 2px;
            background-color: black;
            top: -5px;
        }

        /* 최소/최대값 레이블 */
        .labels {
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
        }

        .labels span {
            font-size: 12px;
        }


        .hidden-slider {
            position: absolute;
            opacity: 0; /* 완전히 숨김 */
        }

        /* 목차 스타일 추가 */
        .main-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin: 15px 0 8px 0;
            padding: 6px 10px;
            border-left: 5px solid #3498db;
            background-color: #f0f7fc;
            border-radius: 0 4px 4px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .main-title::before {
            content: "■";
            color: #3498db;
            margin-right: 8px;
            font-size: 16px;
        }

        /* 그리드 레이아웃 */
        .report-grid {
            display: grid;
            margin-top: 10px;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .report-grid-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 12px;
            background-color: white;
            width: 100%;
            box-sizing: border-box;
        }

        /* 보고서 카드 스타일 */
        .report-card {
            display: flex;
            align-items: center;
            border: 1px solid #828282;
            border-radius: 8px;
            padding: 15px;
            background-color: #f9f9f9;
        }


        .card-content h3 {
            font-size: 18px;
            margin: 0px 0px 10px;
        }

        .card-content p {
            margin: 5px 0;
            font-size: 14px;
        }

        .card-icon {
            flex: 3; /* 이미지 영역의 비율 */
            text-align: right; /* 이미지를 오른쪽 정렬 */
        }

        .card-icon img {
                display: inline-block; /* ← 중요! */
            width: auto;
            height: 60px; /* 이미지 크기 고정 */
            filter: brightness(0);
        }


        /* 차트 캔버스 스타일 수정 */
        .chart-canvas {
            width: 100% !important;
            height: 160px !important;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

        /* 그래프 그리드 스타일 수정 */
        .graph-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
            width: 100%;
            box-sizing: border-box;
        }

        .graph-item {
            background-color: white;
            border-radius: 8px;
            padding: 5px;

            width: 100%;
            box-sizing: border-box;
        }

        .sub-title {
            font-size: 11pt !important;
            font-weight: bold;
        }

        body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        * {
            box-sizing: border-box;
            -moz-box-sizing: border-box;
        }

        #all_content {
            width: 210mm;
            min-height: 297mm;
            padding: 0 10mm 10mm 10mm; /* set contents area */
            margin: 10mm auto;
            border-radius: 5px;
            background: #fff;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);

        }

        #rangeContainer_forward_head_angle {
            margin-bottom: 100px;
        }


        @page {
            size: A4;
            margin: 0;
        }

        @media print {
            * {       /* 인쇄 시 배경 그래픽 강제 적용 */
                -webkit-print-color-adjust: exact !important; /* Chrome, Safari */
                color-adjust: exact !important; /* Firefox */
                print-color-adjust: exact !important; /* 표준 속성 */
            }

            html, body {
                width: 210mm;
                height: 297mm;
            }

            @page {
                margin: 5mm;
            }

            #all_content {
                margin: 0;
                border: initial;
                border-radius: initial;
                width: initial;
                min-height: initial;
                box-shadow: initial;
                page-break-after: always;
            }

            #youtube-video {
                display: none;
            }

            #youtube-videos-container {
                display: none;
            }

            /* Prevent breaking key elements across pages */
            .sub_container,
            .range-container,
            .report-card,
            .graph-item {
                page-break-inside: avoid;
                break-inside: avoid;
            }
        }

    </style>
</head>
<body>
<div id="all_content">
    <!-- 새로운 인쇄 버튼 추가 -->
    <div class="print-button-container" style="position: fixed; top: 20px; right: 20px; z-index: 1000;">
        <button id="print-report" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-full shadow-lg transition duration-200 flex items-center">
            <i class="bi bi-printer mr-2"></i> 인쇄하기
        </button>
    </div>

    <script>
        // 모바일 환경 감지 함수
        function isMobileDevice() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                   (navigator.userAgent.includes('Mobile') && navigator.userAgent.includes('WebView'));
        }

        if (isMobileDevice()) {
            const printContainer = document.querySelector(".print-button-container");
            printContainer.style.display = "none";
        } else {
            document.getElementById('print-report').addEventListener('click', function () {
                // 인쇄 페이지를 새 창으로 열고 자동으로 인쇄 팝업 표시
                // var printWindow = window.open("/body-print/{{ user.id }}/", '_blank');
                const printContainer = document.querySelector(".print-button-container");
                printContainer.style.display = "none";

                window.print();
            });
        }

        // 인쇄 미리보기가 뜨기 직전에 실행
        window.addEventListener('beforeprint', () => {
            const youtubeVideo = document.getElementById('youtube-video');
            youtubeVideo.style.display = 'none';

            const youtubeVideosContainer = document.getElementById('youtube-videos-container');
            youtubeVideosContainer.style.display = 'none';
        });

        // 인쇄(또는 인쇄 미리보기) 창이 닫힐 때 실행
        window.addEventListener('afterprint', () => {
            const youtubeVideo = document.getElementById('youtube-video');
            youtubeVideo.style.display = 'block';

            const youtubeVideosContainer = document.getElementById('youtube-videos-container');
            youtubeVideosContainer.style.display = 'flex';
          });

    </script>

    <div id="top">
        <div>
            <div class="top_left_container"><img src="/static/images/AICU_CI.png" alt="logo">
                <h2>바디스캐너</h2></div>
            <div class="top_left_container"><h1 id="top-text">체형분석 결과</h1></div>
        </div>
        <div class="userinfo">
            <div class="header">
                <div>연령</div>
                <div>성별</div>
                <div>날짜</div>
            </div>
            <div>
                <div>{{ user.dob|calc_age }}</div>
                <div>
                    {% if user.gender == 'M' %}
                        남성
                    {% elif user.gender == 'F' %}
                        여성
                    {% else %}
                        정보없음
                    {% endif %}
                </div>
                {% if date_list|length > 1%}
                <div class="date">
                    <form method="get" id="date-select-form" style="display:inline;">
                        <select name="date" id="date-select" style="padding:3px 8px; border-radius:6px; border:1px solid #ccc;">
                            <option value="" selected>날짜 선택</option>
                            {% for date in date_list %}
                                <option value="{{ date }}" {% if date == selected_date %}selected{% endif %}>{{ date|pretty_date_print }}</option>
                            {% endfor %}
                        </select>
                    </form>
                </div>
                {% else %}
                <div>{{ dates|pretty_date_print }}</div>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
            if (isMobileDevice()) {
            // Django 템플릿에서 세션 유무를 JS 변수로 전달
            const isAuthenticated = {{ user.is_authenticated|yesno:"true,false" }};
            const jwtHeader = `{{ header|default_if_none:"" }}`;

                if (window.location.href.includes('date=')) {
                    document.getElementById('date-select-form').addEventListener('change', function() {
                        document.getElementById('date-select-form').submit();
                        return;
                    });
                }
                else {
                    // 세션이 없을 때만 JWT로 세션 발급
                    document.getElementById('date-select').addEventListener('change', function(e) {
                        const select = document.getElementById('date-select');
                        const selectedDate = select.value;
                        const params = new URLSearchParams(window.location.search);
                        params.set('date', selectedDate);

                        fetch(`${window.location.origin}/api/jwt-to-session/`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${jwtHeader}`
                            },
                            credentials: 'same-origin',
                        })
                        .then(response => {
                            if (response.status === 200) {
                                    document.getElementById('date-select-form').submit();
                                    return;
                            }
                                return response.text().then(text => { throw new Error(text); });
                            })
                        .catch(error => {
                            alert('로그인 인증 정보를 불러올 수 없습니다.\n' + error);
                        });
                    });
                                }
                                
                            } else {
                                document.getElementById('date-select-form').addEventListener('change', function() {
                                    document.getElementById('date-select-form').submit();
                                });
                            }
        </script>

    <!-- 정면 측정결과 , 측면 측정 결과를 포함하는 컨테이너 -->
    <div class="container" style="background-color: white; max-width: none;">
        <div class="sub_container" style="border: 3px solid #000;">
            <h3 class="sub_title">정면 측정결과</h3>

            <!-- 슬라이더 컨테이너 추가 -->
            <div class="left_inside_container">
                <div id="front-sliders-container"></div>
                {% comment %} <img src="/static/images/example_body.svg" alt="Body Scanner" style="width: 100%; height: auto; margin-top: 20px;"> {% endcomment %}
                <canvas id="frontCanvas" width="200" height="400"></canvas>
            </div>
        </div>
        <div class="sub_container" style="border: 3px solid #000;">
            <h3 class="sub_title">측면 측정결과</h3>

            <div class="left_inside_container">
                <div id="side-sliders-container"></div>
                <canvas id="sideCanvas" width="200" height="400"></canvas>
            </div>
        </div>

    </div>


    <!-- 리포트 카드 그리드 -->
    <div class="report-grid">
        {% for item in report_items %}
            {% if item.alias != 'spinal_imbalance' %}
                <div class="report-card">
                    <!-- 텍스트 영역 -->
                    <div class="card-content">
                        <h3>{{ item.title }}</h3>
                        <p><strong>측정 항목:</strong> {{ item.measurement_type }}</p>
                        <p><strong>측정 결과:</strong> {{ item.result }}</p>
                        <p><strong>상세 설명:</strong> {{ item.description }}</p>
                        <p><strong>측정 기준:</strong> {{ item.metric }}</p>
                    </div>

                    <!-- 이미지 영역 -->
                    <div class="card-icon">
                        <img src="{% static 'icons/' %}{{ item.alias }}.svg" alt="{{ item.title }} 아이콘">
                    </div>
                </div>
            {% else %}
                <!-- 척추-어깨 리포트 카드 -->
                <div class="report-card">
                    <!-- 텍스트 영역 -->
                    <div class="card-content">
                        <h3>{{ item.title }} - 척추-어깨</h3>
                        <p><strong>측정 항목:</strong> {{ item.measurement_type|default:item.metric }}</p>
                        <p><strong>측정 결과:</strong> {{ item.result|split_string:0|cut:"· 척추-어깨: " }}</p>
                        <p><strong>상세 설명:</strong> {{ item.description|get_item:0 }}</p>
                        <p><strong>측정 기준:</strong> {{ item.measurement_type|default:item.metric }}</p>
                    </div>

                    <!-- 이미지 영역 -->
                    <div class="card-icon">
                        <img src="{% static 'icons/' %}{{ item.alias }}.svg" alt="{{ item.title }} 아이콘">
                    </div>
                </div>

                <!-- 척추-골반 리포트 카드 -->
                <div class="report-card">
                    <!-- 텍스트 영역 -->
                    <div class="card-content">
                        <h3>{{ item.title }} - 척추-골반</h3>
                        <p><strong>측정 항목:</strong> {{ item.measurement_type|default:item.metric }}</p>
                        <p><strong>측정 결과:</strong> {{ item.result|split_string:1|cut:"· 척추-골반: " }}</p>
                        <p><strong>상세 설명:</strong> {{ item.description|get_item:1 }}</p>
                        <p><strong>측정 기준:</strong> {{ item.measurement_type|default:item.metric }}</p>
                    </div>

                    <!-- 이미지 영역 -->
                    <div class="card-icon">
                        <img src="{% static 'icons/' %}{{ item.alias }}.svg" alt="{{ item.title }} 아이콘">
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    </div>


    <!-- 그래프 그리드 -->
    <div class="main-title">변화 추이</div>
    <div class="graph-grid">
        {% for item in report_items %}
            <div class="graph-item">
                <div class="sub-title">{{ item.title }}</div>
                <canvas id="chart_{{ item.alias }}" class="chart-canvas"></canvas>
            </div>
        {% endfor %}
    </div>

    {% if abnormal_aliases %} 
    <div class="main-title" id="youtube-video">개선 방법</div>
    <div id="youtube-videos-container" style="display: flex; flex-direction: column; align-items: center;">
        <div class="youtube-desc" style="text-align: center; margin-bottom: 10px; font-size: 14px; color: #666;">
            해당 검사 결과를 토대로 스트레칭과 운동을 통해 개선될 수 있는 항목에 한해 개선과 스트레칭을 위한 방법을 설명하는 영상을 볼 수 있습니다.
        </div>
    </div>
    {% endif %}
</div>

<script>
    const youtubeDict = {
        "shoulder_level_angle":"https://www.youtube.com/embed/T4ep9OTzUkc", // 어깨기울기
        "leg_length_ratio": "https://www.youtube.com/embed/RkIbGrtqbaw?si=Wus8quFgIX6ywXMx", // 다리 길이 균형
        "scoliosis_shoulder_ratio": "https://www.youtube.com/embed/mUnSpfItRf0?si=BWKF84NUdgiYbveR", // 척추-어깨 비율
        "scoliosis_hip_ratio": "https://www.youtube.com/embed/9DSRpTix5sM?si=TJnRUpMU4SlTHAAz", // 척추-골반 비율
        "face_level_angle": "https://www.youtube.com/embed/duNgul3heBY?si=nxGAx_ZXTZGwbCIp", //안면 비대칭
        "forward_head_angle": "https://www.youtube.com/embed/kB6Pa26sWhQ?si=KMQYTssGTOdPV-5p", // 거북목(목 기울기)
        "spinal_imbalance": "https://www.youtube.com/embed/q6vROPxAi3E?si=-Xit5diOhSW-mDYr", // 척추 불균형
        // "knee_angle": "https://www.youtube.com/embed/ttttttt", // 무릎 기울기
        "hip_level_angle":"https://www.youtube.com/embed/J0Sg9bV_VrY?si=1ldWgXThImgYesS9" // 골반 기울기
    };
    // abnormal_aliases를 JS 배열로 변환 (Django에서 전달)
    const abnormalAliases = {{ abnormal_aliases|safe }};
    
    const container = document.getElementById("youtube-videos-container");
    abnormalAliases.forEach(alias => {
        if (youtubeDict[alias]) {
            const iframe = document.createElement("iframe");
            iframe.width = "560";
            iframe.height = "315";
            iframe.src = youtubeDict[alias];
            iframe.title = "YouTube video";
            iframe.frameBorder = "0";
            iframe.allow = "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture";
            iframe.allowFullscreen = true;
            iframe.referrerpolicy = "strict-origin-when-cross-origin"
            iframe.style.margin = "10px 0";
            container.appendChild(iframe);
        }
    });
</script>

</div>


</body>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
<script>
    Chart.register(ChartDataLabels);

    {% comment %} window.onload = function() {
        window.print();
    } {% endcomment %}


    // itemTitle : 검사항목 이름
    // itemAlias : 검사항목 영문
    // normalRange : 검사항목 정상범위 [int, int]
    // trendData : 검사항목의 데이터 {dates: ['2025-03-26 hh:mm:ss'], values: [float]}
    // minMaxRange : 검사항목의 min, max 범위 [int, int]


    document.addEventListener('DOMContentLoaded', function () {
        // 데이터를 Django 템플릿에서 전달받습니다.
        let trendDataDict = {{ trend_data_dict|safe }};
        const frontSlidersContainer = document.getElementById('front-sliders-container');
        const sideSlidersContainer = document.getElementById('side-sliders-container');
        let selectedTrendDataIndex;

        // 슬라이더 데이터를 저장할 배열
        const slidersData = [];
        const sideSlidersData = [];

        function sortTrendDataByDate(trendDataDict) {
            // 새로운 객체에 정렬된 데이터를 저장
            const sortedTrendDataDict = {};
        
            Object.entries(trendDataDict).forEach(([key, value]) => {
                // dates 배열이 있고, 정렬할 값 배열이 있는 경우
                if (Array.isArray(value.dates)) {
                    // 날짜와 인덱스 쌍을 만듦
                    const dateWithIndex = value.dates.map((date, idx) => ({ date, idx }));
                    // 날짜 기준 오름차순 정렬
                    dateWithIndex.sort((a, b) => new Date(a.date) - new Date(b.date));
                    // 정렬된 인덱스 배열
                    const sortedIndices = dateWithIndex.map(item => item.idx);
        
                    // dates, values, val1, val2 등 정렬
                    const sortedDates = sortedIndices.map(i => value.dates[i]);
                    const sortedValues = value.values ? sortedIndices.map(i => value.values[i]) : undefined;
                    const sortedVal1 = value.val1 ? sortedIndices.map(i => value.val1[i]) : undefined;
                    const sortedVal2 = value.val2 ? sortedIndices.map(i => value.val2[i]) : undefined;
        
                    // 새로운 value 객체 생성
                    sortedTrendDataDict[key] = {
                        ...value,
                        dates: sortedDates
                    };
                    if (sortedValues) sortedTrendDataDict[key].values = sortedValues;
                    if (sortedVal1) sortedTrendDataDict[key].val1 = sortedVal1;
                    if (sortedVal2) sortedTrendDataDict[key].val2 = sortedVal2;
                } else {
                    // dates 배열이 없으면 그대로 복사
                    sortedTrendDataDict[key] = value;
                }
            });
        
            return sortedTrendDataDict;
        }
        
        // 사용 예시
        trendDataDict = sortTrendDataByDate(trendDataDict);
        console.log("정렬 후 ", trendDataDict);

        /**
        * 측정값을 “중앙 0° 기준” 그래프 스케일(휜다리: +45~-15, 무릎: -15~+15)로 변환한다.
        * 반환값을 그대로 slidersData/sideSlidersData 에 넣으면 됨.
        *
        * @param {string} alias          ex) 'o_x_legs_left', 'knee_angle_right'
        * @param {number} rawValue       원본 currentValue (실제 측정 각도)
        * @param {[number, number]} mm   min‒max 원본 범위
        * @param {[number, number]} nr   normalRange 원본 범위 (배열 길이 2)
        * @returns {minValue, maxValue, currentValue, normalRange}
        */
        function convertDisplayRange(alias, rawValue, mm, nr) {
            const C = 180;                        // 중앙(정상) 각도
            const Δ = C - rawValue;               // +: 안쪽, –: 바깥(역)

            // 공통 helpers
            const scol = (v) => (C - v);          // (180 - v)  : 정상 구간 계산에 재사용

            // ------ 1) 휜 다리 --------------------------------------------------------
            if (alias.startsWith('o_x_legs')) {
                const kIn  = 45 / (C - mm[0]);      // 45° / (180 - 150)  = 1.5
                const kOut = 15 / (mm[1] - C);      // 15° / (195 - 180)  = 1
                const dVal = Δ >= 0 ?  Δ * kIn : Δ * kOut;

                return {
                minValue      : -15,              // 그래프 왼쪽 끝
                maxValue      :  45,              // 그래프 오른쪽 끝
                currentValue  :  dVal,
                normalRange   : [ 0, scol(nr[0]) * kIn ] // [0°, 15°]
                };
            }

            // ------ 2) 무릎 굽어짐 ----------------------------------------------------
            if (alias.startsWith('knee_angle')) {
                const kIn  = 15 / (C - mm[0]);      // 15° / (180 - 160)  = 0.75
                const kOut = 15 / (mm[1] - C);      // 15° / (190 - 180)  = 1.5
                const dVal = Δ >= 0 ?  Δ * kIn : Δ * kOut;

                return {
                minValue      : -15,
                maxValue      :  15,
                currentValue  :  dVal,
                normalRange   : [ 0, scol(nr[0]) * kIn ] // [0°, 7.5°]
                };
            }

            // ------ 3) 그 외 그대로 ----------------------------------------------------
            return {
                minValue      : mm[0],
                maxValue      : mm[1],
                currentValue  : rawValue,
                normalRange   : nr
            };
        }


        // Helper function to add sliders for items with two values
        function addDualValueSlider(dual = true, position, itemTitle, itemAlias, minMaxRange, normalRange, trendData, selectedTrendDataIndex, unit) {
            if (position === 'front' && dual && itemAlias !== 'spinal_imbalance') {
                if (itemAlias === 'o_x_legs') {
                    const converted = convertDisplayRange(itemAlias, trendData.val1[selectedTrendDataIndex], minMaxRange, normalRange);
                    slidersData.push({
                        id: aliasToIdMap[itemAlias + "_left"] || "{{ forloop.counter }}",
                        title: `${itemTitle} (좌)`,
                        alias: `${itemAlias}_left`,
                        minValue: converted.minValue,
                        maxValue: converted.maxValue,
                        normalRange: converted.normalRange,
                        currentValue: converted.currentValue, // 좌측 값
                        unit: unit,
                    });
                    const convertedRight = convertDisplayRange(itemAlias, trendData.val2[selectedTrendDataIndex], minMaxRange, normalRange);
                    slidersData.push({
                        id: aliasToIdMap[itemAlias + "_right"] || "{{ forloop.counter }}",
                        title: `${itemTitle} (우)`,
                        alias: `${itemAlias}_right`,
                        minValue: convertedRight.minValue,
                        maxValue: convertedRight.maxValue,
                        normalRange: convertedRight.normalRange,
                        currentValue: convertedRight.currentValue, // 우측 값
                        unit: unit,
                    });
                } else {
                    slidersData.push({
                    id: aliasToIdMap[itemAlias + "_left"] || "{{ forloop.counter }}",
                    title: `${itemTitle} (좌)`,
                    alias: `${itemAlias}_left`,
                    minValue: minMaxRange[0],
                    maxValue: minMaxRange[1],
                    normalRange: normalRange,
                    currentValue: trendData.val1[selectedTrendDataIndex], // 좌측 값
                    unit: unit,
                    
                });
                slidersData.push({
                    id: aliasToIdMap[itemAlias + "_right"] || "{{ forloop.counter }}",
                    title: `${itemTitle} (우)`,
                    alias: `${itemAlias}_right`,
                    minValue: minMaxRange[0],
                    maxValue: minMaxRange[1],
                    normalRange: normalRange,
                    currentValue: trendData.val2[selectedTrendDataIndex], // 우측 값
                    unit: unit,
                });
            }
            } else if (position === 'side' && dual && itemAlias !== 'spinal_imbalance') {
                if (itemAlias === 'knee_angle') {
                    const converted = convertDisplayRange(itemAlias + "_left", trendData.val1[selectedTrendDataIndex], minMaxRange, normalRange);
                    sideSlidersData.push({
                        id: aliasToIdMap[itemAlias + "_left"] || "{{ forloop.counter }}",
                        title: `${itemTitle} (좌)`,
                        alias: `${itemAlias}_left`,
                        minValue: converted.minValue,
                        maxValue: converted.maxValue,
                        normalRange: converted.normalRange,
                        currentValue: converted.currentValue, // 좌측 값
                        unit: unit,
                    });
                    const convertedRight = convertDisplayRange(itemAlias + "_right", trendData.val2[selectedTrendDataIndex], minMaxRange, normalRange);
                    sideSlidersData.push({
                        id: aliasToIdMap[itemAlias + "_right"] || "{{ forloop.counter }}",
                        title: `${itemTitle} (우)`,
                        alias: `${itemAlias}_right`,
                        minValue: convertedRight.minValue,
                        maxValue: convertedRight.maxValue,
                        normalRange: convertedRight.normalRange,
                        currentValue: convertedRight.currentValue, // 우측 값
                        unit: unit,
                    }); 
                } else {
                sideSlidersData.push({
                    id: aliasToIdMap[itemAlias + "_left"] || "{{ forloop.counter }}",
                    title: `${itemTitle} (좌)`,
                    alias: `${itemAlias}_left`,
                    minValue: minMaxRange[0],
                    maxValue: minMaxRange[1],
                    normalRange: normalRange,
                    currentValue: trendData.val1[selectedTrendDataIndex], // 좌측 값
                    unit: unit,
                });
                sideSlidersData.push({
                    id: aliasToIdMap[itemAlias + "_right"] || "{{ forloop.counter }}",
                    title: `${itemTitle} (우)`,
                    alias: `${itemAlias}_right`,
                    minValue: minMaxRange[0],
                    maxValue: minMaxRange[1],
                    normalRange: normalRange,
                    currentValue: trendData.val2[selectedTrendDataIndex], // 우측 값
                    unit: unit,
                });
            } 
        } else if (itemAlias === 'spinal_imbalance' && position === 'front' && dual) {
                // spinal_imbalance의 경우
                const parts = ['어깨', '골반'];
                parts.forEach((part, index) => {
                    const currentValue = trendData[`val${index + 1}`][selectedTrendDataIndex]; // val1 또는 val2
                    const partAlias = (part === '어깨') ? 'scoliosis_shoulder_ratio' : 'scoliosis_hip_ratio';
                    const title = `척추-${part} 정렬비`; // 제목 설정

                    slidersData.push({
                        id: aliasToIdMap[partAlias] || "{{ forloop.counter }}",
                        title: title,
                        alias: partAlias,
                        minValue: minMaxRange[0],
                        maxValue: minMaxRange[1],
                        normalRange: normalRange,
                        currentValue: currentValue,
                        unit: unit,
                    });
                });
            } else {
                // 단일 값인 경우
                sideSlidersData.push({
                    id: aliasToIdMap[itemAlias] || "{{ forloop.counter }}", // alias에 따라 id 설정
                    title: itemTitle,
                    alias: itemAlias,
                    minValue: minMaxRange[0],
                    maxValue: minMaxRange[1],
                    normalRange: normalRange,
                    currentValue: trendData.values ? trendData.values[selectedTrendDataIndex] : 0,
                    unit: unit,
                });
            }
        }

        // spinal_imbalance - 척추 불균형
        // scoliosis_hip_ratio : 척추-골반
        // scoliosis_shoulder_ratio
        // 미리 지정한 id와 alias의 매핑
        const aliasToIdMap = {
            'face_level_angle': '1',
            'shoulder_level_angle': '2',
            'scoliosis_shoulder_ratio': '3',
            'scoliosis_hip_ratio': '4',
            'hip_level_angle': '5',
            'o_x_legs_left': '6',
            'o_x_legs_right': '7',
            'leg_length_ratio': '8',
            'forward_head_angle': '1',
            'knee_angle_left': '2',
            'knee_angle_right': '3',
        };
      
        console.log("tt",trendDataDict);
        console.log('선택된 날짜: {{selected_date}}');        

        // 각 report_item에 대해 슬라이더 데이터 생성
        {% for item in report_items %}
            var itemTitle = '{{ item.title }}'; // 검사항목 이름
            var itemAlias = '{{ item.alias }}'; // 검사항목 영문 이름
            var normalRange = {{ item.normal_range|safe }}; // 정상 범위 [min, max]
            var minMaxRange = {{ item.value_range|safe }}; // 전체 범위 [min, max]
            var trendData = trendDataDict[itemAlias]; // 검사항목의 트렌드 데이터

            //console.log("{{item.metric}}");
            //console.log("{{item.unit}}")
            // console.log(normalRange);
             
            // 가장 최근 날짜 데이터 가져오기 (마지막 인덱스 사용)
            if ('{{selected_date}}') {
                selectedTrendDataIndex = trendData.dates.indexOf('{{selected_date}}');
            } else {
                selectedTrendDataIndex = trendData.dates.length - 1;
            }
            // console.log(trendData.dates[selectedTrendDataIndex]); 선택된 인덱스의 value
            var currentValue = 0;

            console.log({{abnormal_aliases|safe}});

            // 항목 유형에 따라 현재 값 설정
            if (itemAlias === 'o_x_legs') { // 휜 다리
                // 두 값이 있는 경우 (예: 양쪽 무릎 각도) 
                addDualValueSlider(true, "front", itemTitle, itemAlias, minMaxRange, normalRange, trendData, selectedTrendDataIndex, "{{ item.unit }}");
            } else if (itemAlias === 'knee_angle') { // 무릎 기울기
                addDualValueSlider(true, "side", itemTitle, itemAlias, minMaxRange, normalRange, trendData, selectedTrendDataIndex, "{{ item.unit }}");
            } else if (itemAlias === 'forward_head_angle') { // 거북목
                addDualValueSlider(false, "side", itemTitle, itemAlias, minMaxRange, normalRange, trendData, selectedTrendDataIndex, "{{ item.unit }}");
            } else if (itemAlias === 'spinal_imbalance') { // 척추 불균형
                addDualValueSlider(true, "front", itemTitle, itemAlias, minMaxRange, normalRange, trendData, selectedTrendDataIndex, "{{ item.unit }}");
            } else {
                // 단일 값인 경우
                currentValue = trendData.values ? trendData.values[selectedTrendDataIndex] : 0;
                slidersData.push({
                    id: aliasToIdMap[itemAlias] || "{{ forloop.counter }}", // alias에 따라 id 설정
                    title: itemTitle,
                    alias: itemAlias,
                    minValue: minMaxRange[0],
                    maxValue: minMaxRange[1],
                    normalRange: normalRange,
                    currentValue: currentValue,
                    unit: "{{ item.unit }}",
                });
            }
            // 슬라이더 데이터 추가

            console.log(slidersData);
            
        {% endfor %}

        // 모든 슬라이더 생성
        createSliders(slidersData, frontSlidersContainer);
        createSliders(sideSlidersData, sideSlidersContainer);
        console.log(slidersData);
        console.log(sideSlidersData);

        /**
         * 슬라이더 생성 함수
         * @param {Array} slidersData - 슬라이더 데이터를 포함한 배열
         * @param {HTMLElement} container - 슬라이더를 추가할 컨테이너
         */
        function createSliders(slidersData, container) {
            // 측면 슬라이더인 경우 빈 공간 추가
            if (container === sideSlidersContainer) {
                const emptySpace = document.createElement('div');
                emptySpace.style.width = '20px';
                emptySpace.style.height = '100%';
                container.appendChild(emptySpace);
            }


            // id를 기준으로 슬라이더 데이터 정렬
            slidersData.sort((a, b) => parseInt(a.id) - parseInt(b.id));

            slidersData.forEach(item => {
                // 슬라이더 컨테이너 생성
                const sliderContainer = document.createElement('div');
                sliderContainer.className = 'range-container';
                sliderContainer.id = `rangeContainer_${item.alias}`;

                // 제목과 현재값 표시 요소 생성
                const titleElement = document.createElement('p');
                const valueDirection = item.currentValue > 0 ? '우' : '좌';
                const absValue = Math.abs(item.currentValue).toFixed(2);
                titleElement.innerHTML = `${item.id}. <b>${item.title}</b> <span id="currentValue${item.id}">${valueDirection} ${absValue}${item.unit}</span>`;

                // 슬라이더 wrapper 생성
                const sliderWrapper = document.createElement('div');
                sliderWrapper.className = 'slider-wrapper';

                // 트랙 (배경색 포함) 생성
                const track = document.createElement('div');
                track.className = 'track';
                track.id = (container === frontSlidersContainer) ? `track${item.id}` : `sideTrack${item.id}`;

                // 인디케이터 (현재 값 표시) 생성
                const indicator = document.createElement('div');
                indicator.className = 'indicator';
                indicator.id = (container === frontSlidersContainer) ? `indicator${item.id}` : `sideIndicator${item.id}`;

                // 라벨 (최소, 최대값) 생성
                const labels = document.createElement('div');
                labels.className = 'labels';

                if (item.alias.includes("o_x_legs")) { // o_x_legs... 로 시작하는 경우 휜 다리
                    labels.innerHTML = `
                                        <span id="minLabel${item.id}">안 ${(Math.abs(parseFloat(item.minValue)).toFixed(1))}${item.unit}</span>
                                        <span id="maxLabel${item.id}">밖 ${parseFloat(item.maxValue).toFixed(1)}${item.unit}</span>
                                    `;
                } else if (item.alias.includes('knee_angle')) { // knee_angle... 로 시작하는 경우 무릎 기울기
                    labels.innerHTML = `
                                        <span id="minLabel${item.id}">역 ${(Math.abs(parseFloat(item.minValue)).toFixed(1))}${item.unit}</span>
                                        <span id="maxLabel${item.id}">정 ${parseFloat(item.maxValue).toFixed(1)}${item.unit}</span>
                                    `;
                }

                else {
                        labels.innerHTML = `
                                        <span id="minLabel${item.id}">좌 ${(Math.abs(parseFloat(item.minValue)).toFixed(1))}${item.unit}</span>
                                        <span id="maxLabel${item.id}">우 ${parseFloat(item.maxValue).toFixed(1)}${item.unit}</span>
                                    `;
                }
                
                // 숨겨진 슬라이더 (필요시에만) 생성
                const slider = document.createElement('input');
                slider.type = 'range';
                slider.className = 'hidden-slider';
                slider.id = `rangeSlider${item.id}`;
                slider.min = item.minValue;
                slider.max = item.maxValue;
                slider.step = 0.01;
                slider.value = item.currentValue;

                // 요소 조립
                sliderWrapper.appendChild(track);
                sliderWrapper.appendChild(indicator);

                sliderContainer.appendChild(titleElement);
                sliderContainer.appendChild(sliderWrapper);
                sliderContainer.appendChild(labels);
                sliderContainer.appendChild(slider);

                // 슬라이더 컨테이너에 추가
                container.appendChild(sliderContainer);

                position = (container === frontSlidersContainer) ? 'front' : 'side';


                // 트랙 색상 및 인디케이터 위치 설정
                updateTrackColors(position, item.id, item.minValue, item.maxValue, item.normalRange);
                updateSliderPosition(position, item.id, item.minValue, item.maxValue, item.currentValue);
            });
        }


        /**
         * 트랙 색상 업데이트 함수
         * @param {string} id - 슬라이더 ID
         * @param {number} minValue - 전체 범위 최소값
         * @param {number} maxValue - 전체 범위 최대값
         * @param {Array} normalRange - 정상 범위 [min, max]
         */
        function updateTrackColors(position, id, minValue, maxValue, normalRange) {
            let track;
            minValue = parseFloat(minValue);
            maxValue = parseFloat(maxValue);


            if (position === 'front') {
                track = document.getElementById(`track${id}`);
            } else {
                track = document.getElementById(`sideTrack${id}`);
            }
            if (!track) {
                console.error(`Track not found for ID: sideTrack${id}`);
                return;
            }
            const normalStartPercent = ((normalRange[0] - minValue) / (maxValue - minValue)) * 100;
            const normalEndPercent = ((normalRange[1] - minValue) / (maxValue - minValue)) * 100;

            track.style.background = `
                                  linear-gradient(to right,
                                      red ${normalStartPercent}%,
                                      #38ACFF ${normalStartPercent}%,
                                      #38ACFF ${normalEndPercent}%,
                                      red ${normalEndPercent}%
                                  )
                                `;
            //console.log(`Updating track colors for ID: sideTrack${id}, min: ${minValue}, max: ${maxValue}, normalRange: ${normalRange}`);
        }

        /**
         * 슬라이더 위치 업데이트 함수 (현재 값 기준)
         * @param {string} id - 슬라이더 ID
         * @param {number} minValue - 전체 범위 최소값
         * @param {number} maxValue - 전체 범위 최대값
         * @param {number} value - 현재 값
         */
        function updateSliderPosition(position, id, minValue, maxValue, value) {
            let indicator; // indicator 변수를 여기서 선언

            minValue = parseFloat(minValue);
            maxValue = parseFloat(maxValue);

            if (position === 'front') {
                indicator = document.getElementById(`indicator${id}`);
            } else {
                indicator = document.getElementById(`sideIndicator${id}`);
            }

            const percent =
                ((value - minValue) / (maxValue - minValue)) * 100;
                console.log(`${indicator}` + percent);
            if (percent > 0 && percent < 100) {
                indicator.style.left = `${percent}%`;
            } else {
                if (percent <= 0) {
                    indicator.style.left = '0%';
                } else if (percent >= 100) {
                    indicator.style.left = '100%';
                }
            }

            console.log(`Updating slider position for ID: ${id}, value: ${value}`);
        }

        const drawFrontImage = async () => {
            const canvas = document.getElementById('frontCanvas');
            const ctx = canvas.getContext('2d');

            try {
                // Load and draw base SVG
                const svgImage = await loadSVG("{% static 'images/male_front_silhouette.svg' %}");
                const {x, y, scaledWidth, scaledHeight} = drawBaseImage(ctx, canvas, svgImage);
                const centerX = x + scaledWidth / 2;

                // Draw face level angle line
                const faceY = y + scaledHeight * 0.075;
                const faceLevelAngle = trendDataDict['face_level_angle'].values[selectedTrendDataIndex];
                const faceAngleLineLength = scaledWidth * 0.2;
                const faceColor = (faceLevelAngle >= -5 && faceLevelAngle < 5) ? '#38ACFF' : '#FF0202';
                drawAngleLine(ctx, centerX, faceY, faceLevelAngle, faceAngleLineLength, faceColor);

                // 숫자 1 표시
                drawNumberCircle(ctx, centerX + faceAngleLineLength / 2, faceY - 20, "1", '#000000');

                // Draw shoulder level angle line
                const shoulderY = y + scaledHeight * 0.195;
                const shoulderLevelAngle = trendDataDict['shoulder_level_angle'].values[selectedTrendDataIndex];
                const shoulderAngleLineLength = scaledWidth * 0.5;
                const shoulderImbalance = trendDataDict['spinal_imbalance'].val1[selectedTrendDataIndex];
                const shoulderOffset = scaledWidth * (shoulderImbalance / 100);
                const shoulderColor = (shoulderLevelAngle >= -5 && shoulderLevelAngle < 5) ? '#38ACFF' : '#FF0202';
                const {centerX: shoulderCenterX, centerY: shoulderCenterY} =
                    drawAngleLine(ctx, centerX + shoulderOffset, shoulderY, shoulderLevelAngle, shoulderAngleLineLength, shoulderColor);

                // 숫자 2 표시
                drawNumberCircle(ctx, shoulderCenterX + shoulderAngleLineLength / 4, shoulderY - 10, "2", '#000000');

                // Draw hip level angle line
                const hipY = y + scaledHeight * 0.457;
                const hipLevelAngle = trendDataDict['hip_level_angle'].values[selectedTrendDataIndex];
                const hipAngleLineLength = scaledWidth * 0.4;
                const hipImbalance = trendDataDict['spinal_imbalance'].val2[selectedTrendDataIndex];
                const hipOffset = scaledWidth * (hipImbalance / 100);
                const hipColor = (hipLevelAngle >= -3 && hipLevelAngle < 3) ? '#38ACFF' : '#FF0202';
                const {centerX: hipCenterX, centerY: hipCenterY} =
                    drawAngleLine(ctx, centerX + hipOffset, hipY, hipLevelAngle, hipAngleLineLength, hipColor);

                // 숫자 3 표시
                drawNumberCircle(ctx, centerX * 1.2, (shoulderCenterY + hipCenterY) / 2, "3", '#000000');

                // Draw spinal alignment line
                drawSpinalLine(ctx, shoulderCenterX, shoulderCenterY, hipCenterX, hipCenterY, centerX);

                // 숫자 4 표시 (척추 중앙)
                drawNumberCircle(ctx, centerX - 12, (shoulderCenterY + hipCenterY) / 2 + 40, "4", '#000000');

                // 숫자 5 표시 (골반)
                drawNumberCircle(ctx, hipCenterX, hipY + 10, "5", '#000000');

                // Draw leg length and O/X legs
                drawLegs(ctx, centerX, y, scaledWidth, scaledHeight, trendDataDict);

                // 숫자 6, 7 표시 (다리)
                const legStartY = y + scaledHeight * 0.457;
                const leftLegX = centerX - scaledWidth * 0.1 / 2 - 12;
                const rightLegX = centerX + scaledWidth * 0.1 / 2 + 12;

                drawNumberCircle(ctx, leftLegX - 15, legStartY + scaledHeight * 0.3, "6", '#000000');
                drawNumberCircle(ctx, rightLegX + 15, legStartY + scaledHeight * 0.3, "7", '#000000');

                // 숫자 8 표시 (다리 길이)
                drawNumberCircle(ctx, centerX, legStartY + scaledHeight * 0.48 + 10, "8", '#000000');

            } catch (error) {
                console.error('Error loading or drawing front SVG:', error);
            }
        };

        const drawSideImage = async () => {
            const canvas = document.getElementById('sideCanvas');
            const ctx = canvas.getContext('2d');

            try {
                // Load and draw base SVG
                const svgImage = await loadSVG("{% static 'images/male_side_silhouette.svg' %}");
                const {x, y, scaledWidth, scaledHeight} = drawBaseImage(ctx, canvas, svgImage);

                // Draw forward head angle line
                const forwardHeadAngle = trendDataDict['forward_head_angle'].values[selectedTrendDataIndex];
                const headLineLength = scaledHeight * 0.12;
                const chinY = y + scaledHeight * 0.2;
                const chinX = x + scaledWidth * 0.72;
                const headColor = (forwardHeadAngle >= 0 && forwardHeadAngle < 50) ? '#38ACFF' : '#FF0202';
                drawForwardHeadLine(ctx, chinX, chinY, forwardHeadAngle, headLineLength, headColor);

                // 숫자 1 표시
                drawNumberCircle(ctx, chinX + 15, chinY - headLineLength, "1", '#000000');

                // Draw knee angle lines
                const leftKneeX = x + scaledWidth * 0.7;
                const leftKneeY = y + scaledHeight * 0.6;
                const rightKneeX = x + scaledWidth * 0.35;
                const rightKneeY = y + scaledHeight * 0.7;

                // 숫자 2 표시 (왼쪽 무릎)
                drawNumberCircle(ctx, leftKneeX + 15, leftKneeY, "2", '#000000');

                // 숫자 3 표시 (오른쪽 무릎)
                drawNumberCircle(ctx, rightKneeX + 15, rightKneeY, "3", '#000000');

                drawKneeAngles(ctx, x, y, scaledWidth, scaledHeight, trendDataDict);

            } catch (error) {
                console.error('Error loading or drawing side SVG:', error);
            }
        };

// 숫자를 원 안에 그리는 함수
        const drawNumberCircle = (ctx, x, y, number, color) => {
            ctx.beginPath();
            ctx.arc(x, y, 10, 0, Math.PI * 2);
            ctx.fillStyle = color;
            ctx.fill();

            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(number, x, y);
        };


        // Helper functions
        const drawBaseImage = (ctx, canvas, svgImage) => {
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Calculate scaling to maintain aspect ratio
            const scale = Math.min(
                canvas.width / svgImage.width,
                canvas.height / svgImage.height
            );

            const scaledWidth = svgImage.width * scale;
            const scaledHeight = svgImage.height * scale;

            // Center the image
            const x = (canvas.width - scaledWidth) / 2;
            const y = (canvas.height - scaledHeight) / 2;

            // Draw the SVG image with proper scaling
            ctx.drawImage(svgImage, x, y, scaledWidth, scaledHeight);

            return {x, y, scaledWidth, scaledHeight};
        };

        const drawAngleLine = (ctx, centerX, centerY, angle, length, color) => {
            // Save the current context state
            ctx.save();

            // Move to the center point and rotate
            ctx.translate(centerX, centerY);
            ctx.rotate(angle * Math.PI / 180);

            // Draw the line
            ctx.beginPath();
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.moveTo(-length / 2, 0);
            ctx.lineTo(length / 2, 0);
            ctx.stroke();

            // Draw circles at both ends
            ctx.beginPath();
            ctx.fillStyle = color;
            ctx.arc(-length / 2, 0, 3, 0, Math.PI * 2);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(length / 2, 0, 3, 0, Math.PI * 2);
            ctx.fill();

            // Restore the context state
            ctx.restore();

            return {centerX, centerY};
        };

        const drawSpinalLine = (ctx, shoulderX, shoulderY, hipX, hipY, centerX) => {
            // Draw spinal alignment line
            ctx.beginPath();
            ctx.strokeStyle = '#38ACFF';
            ctx.lineWidth = 2;
            ctx.moveTo(shoulderX, shoulderY);
            ctx.lineTo(hipX, hipY);
            ctx.stroke();

            // Draw circles at spinal line points
            ctx.beginPath();
            ctx.fillStyle = '#38ACFF';
            ctx.arc(shoulderX, shoulderY, 3, 0, Math.PI * 2);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(centerX, (shoulderY + hipY) / 2, 3, 0, Math.PI * 2);
            ctx.fill();
        };

        const drawLegs = (ctx, centerX, y, scaledWidth, scaledHeight, trendDataDict) => {
            // Draw leg length ratio lines
            const legLengthRatio = trendDataDict['leg_length_ratio'].values[selectedTrendDataIndex];
            const legStartY = y + scaledHeight * 0.457;
            const baseLegLength = scaledHeight * 0.48;
            const legWidth = scaledWidth * 0.1;

            // Calculate the leg lengths based on the ratio
            const leftLegLength = baseLegLength * (1 + legLengthRatio / 100);
            const rightLegLength = baseLegLength * (1 - legLengthRatio / 100);

            const leftLegX = centerX - legWidth / 2 - 8;
            const rightLegX = centerX + legWidth / 2 + 8;

            // Determine color based on normal range (-3 to 3)
            const legColor = (legLengthRatio >= -3 && legLengthRatio < 3) ? '#38ACFF' : '#FF0202';

            // Draw leg length lines
            drawLegLine(ctx, leftLegX, legStartY, leftLegLength, legColor);
            drawLegLine(ctx, rightLegX, legStartY, rightLegLength, legColor);

            // Draw O/X legs
            const leftOXAngle = trendDataDict['o_x_legs'].val1[selectedTrendDataIndex];
            const rightOXAngle = trendDataDict['o_x_legs'].val2[selectedTrendDataIndex];
            const kneeHeight = legStartY + baseLegLength * 0.565; // Position of knees
            const lineLength = scaledWidth * 0.1; // Length of the horizontal line

            // Determine colors based on normal range (165 to 195)
            const leftOXColor = (leftOXAngle >= 165 && leftOXAngle <= 195) ? '#38ACFF' : '#FF0202';
            const rightOXColor = (rightOXAngle >= 165 && rightOXAngle <= 195) ? '#38ACFF' : '#FF0202';

            // Calculate knee deviation from neutral position
            // For angles > 180: knee moves outward (positive offset)
            // For angles < 180: knee moves inward (negative offset)
            const leftKneeOffset = (leftOXAngle - 180) * lineLength / 15;
            const rightKneeOffset = (rightOXAngle - 180) * lineLength / 15;

            drawKneeDeviation(ctx, leftLegX, kneeHeight, leftKneeOffset, leftOXColor);
            drawKneeDeviation(ctx, rightLegX, kneeHeight, rightKneeOffset, rightOXColor);
        };

        const drawLegLine = (ctx, x, startY, length, color) => {
            // Draw leg line
            ctx.beginPath();
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.moveTo(x, startY);
            ctx.lineTo(x, startY + length);
            ctx.stroke();

            // Draw circles at both ends
            ctx.beginPath();
            ctx.fillStyle = color;
            // Top point
            ctx.arc(x, startY, 3, 0, Math.PI * 2);
            ctx.fill();
            // Bottom point
            ctx.beginPath();
            ctx.arc(x, startY + length, 3, 0, Math.PI * 2);
            ctx.fill();
        };

        const drawKneeDeviation = (ctx, legX, kneeHeight, offset, color) => {
            // Draw knee deviation line
            ctx.beginPath();
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.moveTo(legX, kneeHeight);
            ctx.lineTo(legX + offset, kneeHeight);
            ctx.stroke();

            // Draw circle at non-center knee position
            ctx.beginPath();
            ctx.fillStyle = color;
            ctx.arc(legX + offset, kneeHeight, 3, 0, Math.PI * 2);
            ctx.fill();
        };

        const drawForwardHeadLine = (ctx, chinX, chinY, angle, length, color) => {
            // Save the current context state
            ctx.save();

            // Move to the center point and rotate
            ctx.translate(chinX, chinY);
            ctx.rotate(-angle * Math.PI / 180);

            // Draw the line
            ctx.beginPath();
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.moveTo(0, 0);
            ctx.lineTo(0, -length);
            ctx.stroke();

            // Draw circles at both ends
            ctx.beginPath();
            ctx.fillStyle = color;
            ctx.arc(0, 0, 3, 0, Math.PI * 2);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(0, -length, 3, 0, Math.PI * 2);
            ctx.fill();

            // Restore the context state
            ctx.restore();
        };

        const drawKneeAngles = (ctx, x, y, scaledWidth, scaledHeight, trendDataDict) => {
            // Draw knee angle lines
            const leftKneeAngle = trendDataDict['knee_angle'].val1[selectedTrendDataIndex];
            const rightKneeAngle = trendDataDict['knee_angle'].val2[selectedTrendDataIndex];
            const legLengthRatio = trendDataDict['leg_length_ratio'].values[selectedTrendDataIndex];
            const baseHipToKneeLength = scaledHeight * 0.27;
            const baseKneeToAnkleLength = scaledHeight * 0.375;

            // Calculate leg lengths based on ratio
            const leftHipToKneeLength = baseHipToKneeLength * (1 + legLengthRatio / 100);
            const rightHipToKneeLength = baseHipToKneeLength * 0.4;
            const leftKneeToAnkleLength = baseKneeToAnkleLength * (1 + legLengthRatio / 100);
            const rightKneeToAnkleLength = baseKneeToAnkleLength * (1 - legLengthRatio / 100);

            // Left knee
            const leftHipX = x + scaledWidth * 0.7;
            const leftHipY = y + scaledHeight * 0.457;

            const leftAngleRad = (leftKneeAngle - 180) * Math.PI / 180;
            const leftKneeX = leftHipX + Math.sin(leftAngleRad) * leftHipToKneeLength;
            const leftKneeY = leftHipY + Math.cos(leftAngleRad) * leftHipToKneeLength;

            const leftAnkleX = leftHipX + 5;
            const leftAnkleY = leftHipY + leftHipToKneeLength + leftKneeToAnkleLength - 55;

            // Right knee (moved to the left)
            const rightHipX = x + scaledWidth * 0.35;
            const rightHipY = y + scaledHeight * 0.6;

            const rightAngleRad = (rightKneeAngle - 180) * Math.PI / 180;
            const rightKneeX = rightHipX + Math.sin(rightAngleRad) * rightHipToKneeLength;
            const rightKneeY = rightHipY + Math.cos(rightAngleRad) * rightHipToKneeLength;

            const rightAnkleX = rightHipX + 21;
            const rightAnkleY = rightHipY + rightHipToKneeLength + rightKneeToAnkleLength - 55;

            // Determine colors based on normal range (170 to 180)
            const leftKneeColor = (leftKneeAngle >= 170 && leftKneeAngle < 180) ? '#38ACFF' : '#FF0202';
            const rightKneeColor = (rightKneeAngle >= 170 && rightKneeAngle < 180) ? '#38ACFF' : '#FF0202';

            // Draw left knee lines
            ctx.beginPath();
            ctx.strokeStyle = leftKneeColor;
            ctx.lineWidth = 2;
            ctx.moveTo(leftHipX, leftHipY);
            ctx.lineTo(leftKneeX, leftKneeY);
            ctx.lineTo(leftAnkleX, leftAnkleY);
            ctx.stroke();

            // Draw right knee lines
            ctx.beginPath();
            ctx.strokeStyle = rightKneeColor;
            ctx.lineWidth = 2;
            ctx.moveTo(rightHipX, rightHipY);
            ctx.lineTo(rightKneeX, rightKneeY);
            ctx.lineTo(rightAnkleX, rightAnkleY);
            ctx.stroke();

            // Draw circles at knee positions
            ctx.beginPath();
            ctx.fillStyle = leftKneeColor;
            ctx.arc(leftKneeX, leftKneeY, 3, 0, Math.PI * 2);
            ctx.fill();
            ctx.beginPath();
            ctx.fillStyle = rightKneeColor;
            ctx.arc(rightKneeX, rightKneeY, 3, 0, Math.PI * 2);
            ctx.fill();
        };

        // Load SVG from file
        const loadSVG = (url) => {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => resolve(img);
                img.onerror = (e) => reject(e);
                img.src = url;
            });
        };

        // Initial draw when page loads
        drawFrontImage();
        drawSideImage();


        // 각 차트 초기화
        // Chart.js 및 datalabels 플러그인 로드


// 각 차트 초기화
        {% for item in report_items %}
            const ctx_{{ item.alias }} = document.getElementById('chart_{{ item.alias }}').getContext('2d');
            const normalRange_{{ item.alias }} = {{ item.normal_range|safe }};
            const valueRange_{{ item.alias }} = {{ item.value_range|safe }};

            {% if item.alias == 'spinal_imbalance' or item.alias == 'o_x_legs' or item.alias == 'knee_angle' %}
                // 날짜 포맷 조정
                const formattedDates_{{ item.alias }} = trendDataDict['{{ item.alias }}'].dates.map(date => date.slice(0, 10));

                new Chart(ctx_{{ item.alias }}, {
                    type: 'line',
                    data: {
                        labels: formattedDates_{{ item.alias }},
                        datasets: [
                            {
                                label: trendDataDict['{{ item.alias }}'].part[0], // First dataset (e.g., Left)
                                data: trendDataDict['{{ item.alias }}'].val1,
                                borderColor: 'rgba(54, 162, 235, 1)', // Blue line
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                borderWidth: 2,
                                pointRadius: 6,
                                pointBackgroundColor: function (context) {
                                    const value = context.raw;
                                    return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1]) ? 'rgba(54, 162, 235, 1)' : 'rgba(255, 99, 132, 1)';
                                },
                                pointBorderColor: function (context) {
                                    const value = context.raw;
                                    return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1]) ? 'rgba(54, 162, 235, 1)' : 'rgba(255, 99, 132, 1)';
                                },
                                pointBorderWidth: 2,
                                fill: false,
                                datalabels: { // Datalabels for first dataset - Original Colors, Top Align
                                    align: 'top', // Position above point
                                    anchor: 'end',
                                    color: function (context) { // Original color logic
                                        const value = context.raw;
                                        return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1]) ? 'rgb(175, 67, 103)' : 'rgb(31, 31, 31)';
                                    },
                                    font: {weight: 'bold', size: 10},
                                    formatter: function (value) {
                                        return value.toFixed(2) + '%';
                                    },
                                    display: true,
                                    borderRadius: 10,
                                    backgroundColor: function (context) { // Original background logic
                                        const value = context.raw;
                                        return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1]) ? 'rgba(54, 162, 235, 0.2)' : 'rgba(184, 184, 184, 0.62)';
                                    },
                                    borderWidth: 1,
                                    borderColor: function (context) { // Original border logic
                                        const value = context.raw;
                                        return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1]) ? 'rgb(255, 198, 198)' : 'rgb(0, 0, 0)';
                                    },
                                    padding: {top: 4, right: 6, bottom: 4, left: 6}
                                }
                            },
                            {
                                label: trendDataDict['{{ item.alias }}'].part[1], // Second dataset (e.g., Right)
                                data: trendDataDict['{{ item.alias }}'].val2,
                                borderColor: 'rgba(255, 159, 64, 1)', // Orange line
                                backgroundColor: 'rgba(255, 159, 64, 0.2)',
                                borderWidth: 2,
                                pointRadius: 6,
                                pointBackgroundColor: function (context) {
                                    const value = context.raw;
                                    return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1]) ? 'rgba(255, 159, 64, 1)' : 'rgba(255, 99, 132, 1)';
                                },
                                pointBorderColor: function (context) {
                                    const value = context.raw;
                                    return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1]) ? 'rgba(255, 159, 64, 1)' : 'rgba(255, 99, 132, 1)';
                                },
                                pointBorderWidth: 2,
                                fill: false,
                                datalabels: { // Datalabels for second dataset - Original Colors, Bottom Align
                                    align: 'bottom', // Position below point
                                    anchor: 'end',
                                    color: function (context) { // Original color logic
                                        const value = context.raw;
                                        return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1]) ? 'rgb(175, 67, 103)' : 'rgb(31, 31, 31)';
                                    },
                                    font: {weight: 'bold', size: 10},
                                    formatter: function (value) {
                                        return value.toFixed(2) + '%';
                                    },
                                    display: true,
                                    borderRadius: 10,
                                    backgroundColor: function (context) { // Original background logic
                                        const value = context.raw;
                                        return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1]) ? 'rgba(54, 162, 235, 0.2)' : 'rgba(184, 184, 184, 0.62)';
                                    },
                                    borderWidth: 1,
                                    borderColor: function (context) { // Original border logic
                                        const value = context.raw;
                                        return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1]) ? 'rgb(255, 198, 198)' : 'rgb(0, 0, 0)';
                                    },
                                    padding: {top: 4, right: 6, bottom: 4, left: 6}
                                }
                            }
                        ]
                    },
                    options: { // Keep original options, remove global datalabels plugin config
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            // REMOVED global datalabels config here
                            legend: {
                                display: true,
                                position: 'top',
                            },
                            tooltip: {
                                enabled: true,
                            }
                        },
                        scales: {
                            y: {
                                min: valueRange_{{ item.alias }}[0],
                                max: valueRange_{{ item.alias }}[1],
                                ticks: {
                                    font: {
                                        size: 10
                                    }
                                }
                            },
                            x: {
                                ticks: {
                                    font: {
                                        size: 10
                                    },
                                    maxRotation: 45,
                                    minRotation: 45
                                }
                            }
                        }
                    },
                    plugins: [{ // Keep original plugins array (normalRangeBackground)
                        id: 'normalRangeBackground',
                        beforeDraw: function (chart) {
                            const ctx = chart.ctx;
                            const yAxis = chart.scales.y;
                            const xAxis = chart.scales.x;

                            const yStart = yAxis.getPixelForValue(normalRange_{{ item.alias }}[0]);
                            const yEnd = yAxis.getPixelForValue(normalRange_{{ item.alias }}[1]);

                            ctx.fillStyle = 'rgba(75, 192, 192, 0.2)';
                            ctx.fillRect(xAxis.left, yEnd, xAxis.width, yStart - yEnd);

                            ctx.beginPath();
                            ctx.lineWidth = 1;
                            ctx.strokeStyle = 'rgba(75, 192, 192, 0.5)';
                            ctx.setLineDash([5, 5]);

                            ctx.moveTo(xAxis.left, yStart);
                            ctx.lineTo(xAxis.right, yStart);

                            ctx.moveTo(xAxis.left, yEnd);
                            ctx.lineTo(xAxis.right, yEnd);

                            ctx.stroke();
                            ctx.setLineDash([]);
                        }
                    }]
                });
            {% else %}
                // 단일 값 차트에 대해서도 동일하게 적용
                // 날짜 포맷 조정
                const formattedDates_{{ item.alias }} = trendDataDict['{{ item.alias }}'].dates.map(date => date.slice(0, 10));

                new Chart(ctx_{{ item.alias }}, {
                    type: 'line',
                    data: {
                        labels: formattedDates_{{ item.alias }},
                        datasets: [
                            {
                                label: '{{ item.title }}',
                                data: trendDataDict['{{ item.alias }}'].values,
                                borderColor: 'rgba(54, 162, 235, 1)',
                                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                borderWidth: 2,
                                pointRadius: 6,
                                pointBackgroundColor: function (context) {
                                    const value = context.raw;
                                    // 정상 범위 내의 값은 파란색, 범위 밖의 값은 빨간색
                                    return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1])
                                        ? 'rgba(54, 162, 235, 1)'
                                        : 'rgba(255, 99, 132, 1)';
                                },
                                pointBorderColor: function (context) {
                                    const value = context.raw;
                                    // 정상 범위 내의 값은 파란색, 범위 밖의 값은 빨간색
                                    return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1])
                                        ? 'rgba(54, 162, 235, 1)'
                                        : 'rgba(255, 99, 132, 1)';
                                },

                                pointBorderWidth: 2,
                                fill: false
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            datalabels: {
                                align: 'top',
                                anchor: 'end',
                                color: function (context) {
                                    const value = context.raw;
                                    // 정상 범위 내의 값은 파란색, 범위 밖의 값은 빨간색
                                    return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1])
                                        ? 'rgb(216, 81, 126)'
                                        : 'rgb(31, 31, 31)';
                                },
                                font: {
                                    weight: 'bold',
                                    size: 10
                                },
                                formatter: function (value) {
                                    return value.toFixed(2) + '%';
                                },
                                display: true,
                                // 둥근 컨테이너로 값 감싸기
                                borderRadius: 10,
                                backgroundColor: function (context) {
                                    const value = context.raw;
                                    // 정상 범위 내의 값은 연한 파란색, 범위 밖의 값은 연한 빨간색
                                    return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1])
                                        ? 'rgba(54, 162, 235, 0.2)'
                                        : 'rgba(184, 184, 184, 0.62)';
                                },
                                borderWidth: 1,
                                borderColor: function (context) {
                                    const value = context.raw;
                                    // 정상 범위 내의 값은 파란색, 범위 밖의 값은 빨간색
                                    return (value >= normalRange_{{ item.alias }}[0] && value <= normalRange_{{ item.alias }}[1])
                                        ? 'rgb(255, 198, 198)'
                                        : 'rgb(0, 0, 0)';
                                },
                                padding: {
                                    top: 4,
                                    right: 6,
                                    bottom: 4,
                                    left: 6
                                }
                            },
                        },
                        scales: {
                            y: {
                                min: valueRange_{{ item.alias }}[0],
                                max: valueRange_{{ item.alias }}[1],
                                ticks: {
                                    font: {
                                        size: 10
                                    }
                                }
                            },
                            x: {
                                ticks: {
                                    font: {
                                        size: 10
                                    },
                                    maxRotation: 45,
                                    minRotation: 45
                                }
                            }
                        }
                    },
                    plugins: [{
                        id: 'normalRangeBackground',
                        beforeDraw: function (chart) {
                            const ctx = chart.ctx;
                            const yAxis = chart.scales.y;
                            const xAxis = chart.scales.x;

                            // 정상 범위 시작점과 끝점
                            const yStart = yAxis.getPixelForValue(normalRange_{{ item.alias }}[0]);
                            const yEnd = yAxis.getPixelForValue(normalRange_{{ item.alias }}[1]);

                            // 배경색 칠하기
                            ctx.fillStyle = 'rgba(75, 192, 192, 0.2)'; // 연한 초록색
                            ctx.fillRect(xAxis.left, yEnd, xAxis.width, yStart - yEnd);

                            // 정상 범위 경계선 그리기
                            ctx.beginPath();
                            ctx.lineWidth = 1;
                            ctx.strokeStyle = 'rgba(75, 192, 192, 0.5)';
                            ctx.setLineDash([5, 5]);

                            // 하한선
                            ctx.moveTo(xAxis.left, yStart);
                            ctx.lineTo(xAxis.right, yStart);

                            // 상한선
                            ctx.moveTo(xAxis.left, yEnd);
                            ctx.lineTo(xAxis.right, yEnd);

                            ctx.stroke();
                            ctx.setLineDash([]);

                            // 정상 범위 텍스트 추가
                            // ctx.fillStyle = 'rgba(75, 192, 192, 1)';
                            // ctx.font = '10px Arial';
                            // ctx.textAlign = 'left';
                            // ctx.fillText('정상 범위: ' + normalRange_{{ item.alias }}[0] + ' ~ ' +
                            //            normalRange_{{ item.alias }}[1], xAxis.left + 5, yEnd - 5);

                            // 현재 값 표시 (마지막 데이터 포인트)
                            // const lastValue = chart.data.datasets[0].data[chart.data.datasets[0].data.length - 1];
                            // ctx.fillStyle = 'rgba(255, 99, 132, 1)';
                            // ctx.textAlign = 'right';
                            // ctx.fillText('현재 값: ' + lastValue, xAxis.right - 5, yAxis.top + 15);
                        }
                    }]
                });
            {% endif %}
        {% endfor %}



        function sameHeightContainer() {
            // 모든 left_inside_container에 동일한 높이 적용
            const containers = document.querySelectorAll('.left_inside_container');

            // 첫 번째 컨테이너(정면)의 높이 가져오기
            const height = containers[0].offsetHeight;

            // 모든 컨테이너에 동일한 높이 적용
            containers.forEach(container => {
                container.style.height = height + 'px';

                // 컨테이너 내부 요소들 중앙 정렬
                container.style.display = 'flex';
                container.style.alignItems = 'center';
                container.style.justifyContent = 'center';
            });
        }

        sameHeightContainer()

    });


</script>
</html>
